package com.kbtesting.controller;

import com.kbtesting.config.AppConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.info.BuildProperties;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 系统管理控制器
 * 
 * <AUTHOR> Testing Team
 */
@Tag(name = "系统管理", description = "系统健康检查、配置管理等基础功能")
@RestController
@RequestMapping("/api/system")
@CrossOrigin(origins = "*")
public class SystemController {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemController.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private Environment environment;
    
    @Autowired(required = false)
    private BuildProperties buildProperties;
    
    /**
     * 系统健康检查
     */
    @Operation(summary = "系统健康检查", description = "检查系统整体运行状态和各组件连接情况")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "系统正常",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"status\":\"UP\",\"timestamp\":\"2024-01-01T10:00:00\",\"version\":\"1.0.0\",\"components\":{\"database\":\"UP\",\"dify\":\"UP\"}}"))),
        @ApiResponse(responseCode = "503", description = "系统异常")
    })
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> components = new HashMap<>();
        
        try {
            // 基本信息
            response.put("status", "UP");
            response.put("timestamp", LocalDateTime.now());
            response.put("service", "Knowledge Base Testing System");
            
            // 版本信息
            if (buildProperties != null) {
                response.put("version", buildProperties.getVersion());
                response.put("buildTime", buildProperties.getTime());
            } else {
                response.put("version", "1.0.0-SNAPSHOT");
            }
            
            // 环境信息
            String[] activeProfiles = environment.getActiveProfiles();
            response.put("profiles", activeProfiles.length > 0 ? activeProfiles : new String[]{"default"});
            
            // 组件状态检查
            components.put("application", "UP");
            
            // 检查 Dify 配置
            try {
                String difyUrl = appConfig.getApi().getDify().getBaseUrl();
                String difyApiKey = appConfig.getApi().getDify().getApiKey();
                if (difyUrl != null && !difyUrl.trim().isEmpty() && 
                    difyApiKey != null && !difyApiKey.trim().isEmpty()) {
                    components.put("dify", "UP");
                } else {
                    components.put("dify", "DOWN - 配置缺失");
                }
            } catch (Exception e) {
                components.put("dify", "DOWN - " + e.getMessage());
            }
            
            // 检查知识库配置
            try {
                String kbUrl = appConfig.getApi().getKnowledgeBase().getBaseUrl();
                String kbApiKey = appConfig.getApi().getKnowledgeBase().getApiKey();
                if (kbUrl != null && !kbUrl.trim().isEmpty() && 
                    kbApiKey != null && !kbApiKey.trim().isEmpty()) {
                    components.put("knowledgeBase", "UP");
                } else {
                    components.put("knowledgeBase", "DOWN - 配置缺失");
                }
            } catch (Exception e) {
                components.put("knowledgeBase", "DOWN - " + e.getMessage());
            }
            
            response.put("components", components);
            
            // 判断整体状态
            boolean allUp = components.values().stream()
                .allMatch(status -> "UP".equals(status));
            
            if (allUp) {
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "DEGRADED");
                return ResponseEntity.status(503).body(response);
            }
            
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            response.put("status", "DOWN");
            response.put("error", e.getMessage());
            return ResponseEntity.status(503).body(response);
        }
    }
    
    /**
     * 获取系统信息
     */
    @Operation(summary = "获取系统信息", description = "获取系统版本、配置、运行时信息等")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功")
    })
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 应用信息
            Map<String, Object> app = new HashMap<>();
            app.put("name", "Knowledge Base Testing System");
            app.put("description", "自动化知识库测试系统");
            
            if (buildProperties != null) {
                app.put("version", buildProperties.getVersion());
                app.put("buildTime", buildProperties.getTime());
                app.put("group", buildProperties.getGroup());
                app.put("artifact", buildProperties.getArtifact());
            }
            
            response.put("app", app);
            
            // 环境信息
            Map<String, Object> env = new HashMap<>();
            env.put("profiles", environment.getActiveProfiles());
            env.put("javaVersion", System.getProperty("java.version"));
            env.put("javaVendor", System.getProperty("java.vendor"));
            env.put("osName", System.getProperty("os.name"));
            env.put("osVersion", System.getProperty("os.version"));
            
            response.put("environment", env);
            
            // 运行时信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> runtimeInfo = new HashMap<>();
            runtimeInfo.put("processors", runtime.availableProcessors());
            runtimeInfo.put("maxMemory", runtime.maxMemory() / 1024 / 1024 + " MB");
            runtimeInfo.put("totalMemory", runtime.totalMemory() / 1024 / 1024 + " MB");
            runtimeInfo.put("freeMemory", runtime.freeMemory() / 1024 / 1024 + " MB");
            
            response.put("runtime", runtimeInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取系统信息失败", e);
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 获取配置信息（脱敏）
     */
    @Operation(summary = "获取配置信息", description = "获取系统配置信息（敏感信息已脱敏）")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功")
    })
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> config() {
        try {
            Map<String, Object> response = new HashMap<>();
            
            // API 配置（脱敏）
            Map<String, Object> apiConfig = new HashMap<>();
            
            // Dify 配置
            Map<String, Object> difyConfig = new HashMap<>();
            difyConfig.put("baseUrl", appConfig.getApi().getDify().getBaseUrl());
            difyConfig.put("timeout", appConfig.getApi().getDify().getTimeout());
            difyConfig.put("apiKeyConfigured", 
                appConfig.getApi().getDify().getApiKey() != null && 
                !appConfig.getApi().getDify().getApiKey().trim().isEmpty());
            apiConfig.put("dify", difyConfig);
            
            // 知识库配置
            Map<String, Object> kbConfig = new HashMap<>();
            kbConfig.put("baseUrl", appConfig.getApi().getKnowledgeBase().getBaseUrl());
            kbConfig.put("timeout", appConfig.getApi().getKnowledgeBase().getTimeout());
            kbConfig.put("apiKeyConfigured", 
                appConfig.getApi().getKnowledgeBase().getApiKey() != null && 
                !appConfig.getApi().getKnowledgeBase().getApiKey().trim().isEmpty());
            apiConfig.put("knowledgeBase", kbConfig);
            
            response.put("api", apiConfig);
            
            // 测试配置
            Map<String, Object> testConfig = new HashMap<>();
            testConfig.put("knowledgeBaseTesting", appConfig.getKnowledgeBaseTesting());
            testConfig.put("difyTesting", appConfig.getDifyTesting());
            testConfig.put("testsetGeneration", appConfig.getTestsetGeneration());
            
            response.put("testing", testConfig);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取配置信息失败", e);
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 重新加载配置
     */
    @Operation(summary = "重新加载配置", description = "重新加载系统配置（需要管理员权限）")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "重新加载成功"),
        @ApiResponse(responseCode = "500", description = "重新加载失败")
    })
    @PostMapping("/config/reload")
    public ResponseEntity<Map<String, Object>> reloadConfig() {
        try {
            // 这里可以添加配置重新加载的逻辑
            // 目前只是返回成功状态
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "配置重新加载成功");
            response.put("timestamp", LocalDateTime.now());
            
            logger.info("系统配置已重新加载");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("重新加载配置失败", e);
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }
}
