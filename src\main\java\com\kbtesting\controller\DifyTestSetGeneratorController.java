package com.kbtesting.controller;

import com.kbtesting.model.TestQuestion;
import com.kbtesting.model.TestResult;
import com.kbtesting.service.TestsetGeneratorService;
import com.kbtesting.service.KnowledgeBaseTesterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器
 * 提供REST API接口
 * 
 * <AUTHOR> Testing Team
 */
@RestController
@RequestMapping("/api/testsets")
@CrossOrigin(origins = "*")
public class DifyTestSetGeneratorController {
    
    private static final Logger logger = LoggerFactory.getLogger(DifyTestSetGeneratorController.class);
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    @Autowired
    private KnowledgeBaseTesterService knowledgeBaseTesterService;

    /**
     * 上传文档并生成测试集
     */
    @PostMapping("/generate-testset")
    public ResponseEntity<Map<String, Object>> generateTestset(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "outputPath", required = false) String outputPath) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 保存上传的文件
            String tempDir = "temp/uploads";
            Files.createDirectories(Paths.get(tempDir));
            
            String fileName = file.getOriginalFilename();
            Path filePath = Paths.get(tempDir, fileName);
            file.transferTo(filePath.toFile());
            
            // 生成输出路径
            if (outputPath == null) {
                String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                outputPath = "testsets/generated/" + baseName + "_testset.json";
            }
            
            // 生成测试集
            List<TestQuestion> testset = testsetGeneratorService.generateFromFile(
                filePath.toString(), outputPath);
            
            response.put("success", true);
            response.put("message", "测试集生成成功");
            response.put("testsetPath", outputPath);
            response.put("questionCount", testset.size());
            response.put("testset", testset);
            
            // 清理临时文件
            Files.deleteIfExists(filePath);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("生成测试集失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "生成测试集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量生成测试集
     */
    @PostMapping("/generate-testsets-batch")
    public ResponseEntity<Map<String, Object>> generateTestsetsBatch(
            @RequestParam(value = "inputDir", defaultValue = "data/documents") String inputDir,
            @RequestParam(value = "outputDir", defaultValue = "testsets/generated") String outputDir) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> generatedFiles = testsetGeneratorService.processDirectory(inputDir, outputDir);
            
            response.put("success", true);
            response.put("message", "批量生成完成");
            response.put("generatedFiles", generatedFiles);
            response.put("count", generatedFiles.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("批量生成测试集失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量生成失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 运行单个测试集
     */
    @PostMapping("/run-test")
    public ResponseEntity<Map<String, Object>> runTest(
            @RequestParam("testsetPath") String testsetPath,
            @RequestParam(value = "useAsync", defaultValue = "true") boolean useAsync) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<TestResult> results = knowledgeBaseTesterService.runTest(testsetPath, useAsync);
            
            // 计算统计信息
            Map<String, Object> statistics = calculateStatistics(results);
            
            response.put("success", true);
            response.put("message", "测试完成");
            response.put("results", results);
            response.put("statistics", statistics);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("运行测试失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "运行测试失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取测试集列表
     */
    @GetMapping("/testsets")
    public ResponseEntity<Map<String, Object>> getTestsets(
            @RequestParam(value = "dir", defaultValue = "testsets/generated") String dir) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Path dirPath = Paths.get(dir);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }
            
            List<String> testsetFiles = Files.walk(dirPath)
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .map(path -> path.getFileName().toString())
                .sorted()
                .toList();
            
            response.put("success", true);
            response.put("testsets", testsetFiles);
            response.put("count", testsetFiles.size());
            response.put("directory", dir);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取测试集列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取测试集列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取测试集详情
     */
    @GetMapping("/testset/{filename}")
    public ResponseEntity<Map<String, Object>> getTestsetDetail(
            @PathVariable String filename,
            @RequestParam(value = "dir", defaultValue = "testsets/generated") String dir) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String testsetPath = Paths.get(dir, filename).toString();
            List<TestQuestion> testset = testsetGeneratorService.loadTestset(testsetPath);
            
            response.put("success", true);
            response.put("filename", filename);
            response.put("path", testsetPath);
            response.put("questionCount", testset.size());
            response.put("testset", testset);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取测试集详情失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取测试集详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "healthy");
        response.put("timestamp", System.currentTimeMillis());
        response.put("service", "Knowledge Base Testing System");
        response.put("version", "1.0.0");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 计算测试统计信息
     */
    private Map<String, Object> calculateStatistics(List<TestResult> results) {
        Map<String, Object> stats = new HashMap<>();
        
        int totalQuestions = results.size();
        long successfulQueries = results.stream()
            .mapToLong(r -> r.isSuccess() ? 1 : 0)
            .sum();
        
        double successRate = totalQuestions > 0 ? (double) successfulQueries / totalQuestions : 0.0;
        
        double avgResponseTime = results.stream()
            .mapToLong(TestResult::getResponseTime)
            .average()
            .orElse(0.0);
        
        stats.put("totalQuestions", totalQuestions);
        stats.put("successfulQueries", successfulQueries);
        stats.put("successRate", successRate);
        stats.put("averageResponseTime", avgResponseTime);
        
        return stats;
    }
}
