#!/bin/bash

# Dify 知识库 API 测试脚本
# 使用方法: ./scripts/test-dify-api.sh

set -e

# 配置
BASE_URL="http://localhost:8080/kb-testing/api/dify-test"
TEST_KB_NAME="API测试知识库_$(date +%s)"
TEST_DOC_NAME="测试文档"
TEST_DOC_CONTENT="这是一个测试文档的内容。它包含了关于人工智能和机器学习的基本信息。人工智能是计算机科学的一个分支，机器学习是人工智能的一个子集。"

echo "🚀 开始 Dify 知识库 API 测试"
echo "基础URL: $BASE_URL"
echo "测试知识库名称: $TEST_KB_NAME"
echo ""

# 1. 健康检查
echo "1️⃣ 健康检查..."
curl -s "$BASE_URL/health" | jq '.'
echo ""

# 2. 创建知识库
echo "2️⃣ 创建知识库..."
KB_RESPONSE=$(curl -s -X POST "$BASE_URL/knowledge-bases" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"$TEST_KB_NAME\",
    \"description\": \"API测试创建的知识库\"
  }")

echo "$KB_RESPONSE" | jq '.'

# 提取知识库ID
KB_ID=$(echo "$KB_RESPONSE" | jq -r '.knowledgeBase.id // empty')

if [ -z "$KB_ID" ] || [ "$KB_ID" = "null" ]; then
    echo "❌ 创建知识库失败，无法获取知识库ID"
    exit 1
fi

echo "✅ 知识库创建成功，ID: $KB_ID"
echo ""

# 3. 通过文本创建文档
echo "3️⃣ 通过文本创建文档..."
DOC_RESPONSE=$(curl -s -X POST "$BASE_URL/knowledge-bases/$KB_ID/documents/text" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"$TEST_DOC_NAME\",
    \"text\": \"$TEST_DOC_CONTENT\"
  }")

echo "$DOC_RESPONSE" | jq '.'

# 提取文档ID
DOC_ID=$(echo "$DOC_RESPONSE" | jq -r '.document.id // empty')

if [ -z "$DOC_ID" ] || [ "$DOC_ID" = "null" ]; then
    echo "❌ 创建文档失败，无法获取文档ID"
else
    echo "✅ 文档创建成功，ID: $DOC_ID"
fi
echo ""

# 4. 等待文档索引
echo "4️⃣ 等待文档索引完成..."
sleep 10
echo "✅ 等待完成"
echo ""

# 5. 获取知识库详情
echo "5️⃣ 获取知识库详情..."
curl -s "$BASE_URL/knowledge-bases/$KB_ID" | jq '.'
echo ""

# 6. 获取文档列表
echo "6️⃣ 获取文档列表..."
curl -s "$BASE_URL/knowledge-bases/$KB_ID/documents" | jq '.'
echo ""

# 7. 检索测试
echo "7️⃣ 执行检索测试..."
RETRIEVAL_RESPONSE=$(curl -s -X POST "$BASE_URL/knowledge-bases/$KB_ID/retrieve" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是人工智能？",
    "topK": 5,
    "scoreThreshold": 0.5
  }')

echo "$RETRIEVAL_RESPONSE" | jq '.'
echo ""

# 8. 批量检索测试
echo "8️⃣ 执行批量检索测试..."
BATCH_TEST_RESPONSE=$(curl -s -X POST "$BASE_URL/knowledge-bases/$KB_ID/test-retrieval" \
  -H "Content-Type: application/json" \
  -d '{
    "questions": [
      {
        "question": "什么是人工智能？",
        "expectedAnswer": "人工智能是计算机科学的一个分支"
      },
      {
        "question": "什么是机器学习？",
        "expectedAnswer": "机器学习是人工智能的一个子集"
      }
    ]
  }')

echo "$BATCH_TEST_RESPONSE" | jq '.'
echo ""

# 9. 获取文档分段（如果文档创建成功）
if [ ! -z "$DOC_ID" ] && [ "$DOC_ID" != "null" ]; then
    echo "9️⃣ 获取文档分段..."
    curl -s "$BASE_URL/knowledge-bases/$KB_ID/documents/$DOC_ID/segments" | jq '.'
    echo ""
fi

# 10. 清理测试数据
echo "🧹 清理测试数据..."
DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/knowledge-bases/$KB_ID")
echo "$DELETE_RESPONSE" | jq '.'

if [ "$(echo "$DELETE_RESPONSE" | jq -r '.success')" = "true" ]; then
    echo "✅ 知识库删除成功"
else
    echo "⚠️ 知识库删除失败，请手动清理: $KB_ID"
fi
echo ""

echo "🎉 Dify 知识库 API 测试完成！"
echo ""
echo "📊 测试总结:"
echo "- 知识库创建: $([ ! -z "$KB_ID" ] && echo "✅ 成功" || echo "❌ 失败")"
echo "- 文档创建: $([ ! -z "$DOC_ID" ] && echo "✅ 成功" || echo "❌ 失败")"
echo "- 检索功能: $(echo "$RETRIEVAL_RESPONSE" | jq -r '.success // "未知"' | sed 's/true/✅ 成功/g; s/false/❌ 失败/g')"
echo "- 批量测试: $(echo "$BATCH_TEST_RESPONSE" | jq -r '.success // "未知"' | sed 's/true/✅ 成功/g; s/false/❌ 失败/g')"
echo ""
echo "如果测试失败，请检查:"
echo "1. Dify 服务是否正常运行"
echo "2. API 配置是否正确"
echo "3. 网络连接是否正常"
echo "4. API 密钥是否有效"
