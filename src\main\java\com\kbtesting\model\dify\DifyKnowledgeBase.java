package com.kbtesting.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Dify 知识库模型
 * 
 * <AUTHOR> Testing Team
 */
public class DifyKnowledgeBase {
    
    private String id;
    private String name;
    private String description;
    private String permission;
    
    @JsonProperty("data_source_type")
    private String dataSourceType;
    
    @JsonProperty("indexing_technique")
    private String indexingTechnique;
    
    @JsonProperty("app_count")
    private Integer appCount;
    
    @JsonProperty("document_count")
    private Integer documentCount;
    
    @JsonProperty("word_count")
    private Integer wordCount;
    
    @JsonProperty("created_by")
    private String createdBy;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("embedding_model")
    private String embeddingModel;
    
    @JsonProperty("embedding_model_provider")
    private String embeddingModelProvider;
    
    @JsonProperty("embedding_available")
    private Boolean embeddingAvailable;
    
    @JsonProperty("retrieval_model")
    private RetrievalModel retrievalModel;
    
    // 构造函数
    public DifyKnowledgeBase() {}
    
    public DifyKnowledgeBase(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    // Getter 和 Setter 方法
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getPermission() { return permission; }
    public void setPermission(String permission) { this.permission = permission; }
    
    public String getDataSourceType() { return dataSourceType; }
    public void setDataSourceType(String dataSourceType) { this.dataSourceType = dataSourceType; }
    
    public String getIndexingTechnique() { return indexingTechnique; }
    public void setIndexingTechnique(String indexingTechnique) { this.indexingTechnique = indexingTechnique; }
    
    public Integer getAppCount() { return appCount; }
    public void setAppCount(Integer appCount) { this.appCount = appCount; }
    
    public Integer getDocumentCount() { return documentCount; }
    public void setDocumentCount(Integer documentCount) { this.documentCount = documentCount; }
    
    public Integer getWordCount() { return wordCount; }
    public void setWordCount(Integer wordCount) { this.wordCount = wordCount; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public String getEmbeddingModel() { return embeddingModel; }
    public void setEmbeddingModel(String embeddingModel) { this.embeddingModel = embeddingModel; }
    
    public String getEmbeddingModelProvider() { return embeddingModelProvider; }
    public void setEmbeddingModelProvider(String embeddingModelProvider) { this.embeddingModelProvider = embeddingModelProvider; }
    
    public Boolean getEmbeddingAvailable() { return embeddingAvailable; }
    public void setEmbeddingAvailable(Boolean embeddingAvailable) { this.embeddingAvailable = embeddingAvailable; }
    
    public RetrievalModel getRetrievalModel() { return retrievalModel; }
    public void setRetrievalModel(RetrievalModel retrievalModel) { this.retrievalModel = retrievalModel; }
    
    @Override
    public String toString() {
        return "DifyKnowledgeBase{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", documentCount=" + documentCount +
                ", wordCount=" + wordCount +
                '}';
    }
    
    /**
     * 检索模型配置
     */
    public static class RetrievalModel {
        @JsonProperty("search_method")
        private String searchMethod;
        
        @JsonProperty("reranking_enable")
        private Boolean rerankingEnable;
        
        @JsonProperty("reranking_model")
        private RerankingModel rerankingModel;
        
        @JsonProperty("top_k")
        private Integer topK;
        
        @JsonProperty("score_threshold_enabled")
        private Boolean scoreThresholdEnabled;
        
        @JsonProperty("score_threshold")
        private Double scoreThreshold;
        
        // Getter 和 Setter 方法
        public String getSearchMethod() { return searchMethod; }
        public void setSearchMethod(String searchMethod) { this.searchMethod = searchMethod; }
        
        public Boolean getRerankingEnable() { return rerankingEnable; }
        public void setRerankingEnable(Boolean rerankingEnable) { this.rerankingEnable = rerankingEnable; }
        
        public RerankingModel getRerankingModel() { return rerankingModel; }
        public void setRerankingModel(RerankingModel rerankingModel) { this.rerankingModel = rerankingModel; }
        
        public Integer getTopK() { return topK; }
        public void setTopK(Integer topK) { this.topK = topK; }
        
        public Boolean getScoreThresholdEnabled() { return scoreThresholdEnabled; }
        public void setScoreThresholdEnabled(Boolean scoreThresholdEnabled) { this.scoreThresholdEnabled = scoreThresholdEnabled; }
        
        public Double getScoreThreshold() { return scoreThreshold; }
        public void setScoreThreshold(Double scoreThreshold) { this.scoreThreshold = scoreThreshold; }
    }
    
    /**
     * 重排序模型配置
     */
    public static class RerankingModel {
        @JsonProperty("reranking_provider_name")
        private String rerankingProviderName;
        
        @JsonProperty("reranking_model_name")
        private String rerankingModelName;
        
        // Getter 和 Setter 方法
        public String getRerankingProviderName() { return rerankingProviderName; }
        public void setRerankingProviderName(String rerankingProviderName) { this.rerankingProviderName = rerankingProviderName; }
        
        public String getRerankingModelName() { return rerankingModelName; }
        public void setRerankingModelName(String rerankingModelName) { this.rerankingModelName = rerankingModelName; }
    }
}
