@echo off
REM Dify 知识库 API 测试脚本 (Windows 版本)
REM 使用方法: scripts\test-dify-api.bat

setlocal enabledelayedexpansion

REM 配置
set BASE_URL=http://localhost:8080/kb-testing/api/dify-test
set TEST_KB_NAME=API测试知识库_%RANDOM%
set TEST_DOC_NAME=测试文档
set TEST_DOC_CONTENT=这是一个测试文档的内容。它包含了关于人工智能和机器学习的基本信息。人工智能是计算机科学的一个分支，机器学习是人工智能的一个子集。

echo 🚀 开始 Dify 知识库 API 测试
echo 基础URL: %BASE_URL%
echo 测试知识库名称: %TEST_KB_NAME%
echo.

REM 检查 curl 是否可用
curl --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 curl 命令，请安装 curl 或使用 Git Bash
    pause
    exit /b 1
)

REM 1. 健康检查
echo 1️⃣ 健康检查...
curl -s "%BASE_URL%/health"
echo.
echo.

REM 2. 创建知识库
echo 2️⃣ 创建知识库...
curl -s -X POST "%BASE_URL%/knowledge-bases" ^
  -H "Content-Type: application/json" ^
  -d "{\"name\": \"%TEST_KB_NAME%\", \"description\": \"API测试创建的知识库\"}" > kb_response.json

type kb_response.json
echo.

REM 提取知识库ID (简化版本，实际使用中可能需要 jq 或其他 JSON 解析工具)
for /f "tokens=2 delims=:" %%a in ('findstr "\"id\"" kb_response.json') do (
    set KB_ID=%%a
    set KB_ID=!KB_ID:"=!
    set KB_ID=!KB_ID:,=!
    set KB_ID=!KB_ID: =!
)

if "%KB_ID%"=="" (
    echo ❌ 创建知识库失败，无法获取知识库ID
    pause
    exit /b 1
)

echo ✅ 知识库创建成功，ID: %KB_ID%
echo.

REM 3. 通过文本创建文档
echo 3️⃣ 通过文本创建文档...
curl -s -X POST "%BASE_URL%/knowledge-bases/%KB_ID%/documents/text" ^
  -H "Content-Type: application/json" ^
  -d "{\"name\": \"%TEST_DOC_NAME%\", \"text\": \"%TEST_DOC_CONTENT%\"}" > doc_response.json

type doc_response.json
echo.

REM 提取文档ID
for /f "tokens=2 delims=:" %%a in ('findstr "\"id\"" doc_response.json') do (
    set DOC_ID=%%a
    set DOC_ID=!DOC_ID:"=!
    set DOC_ID=!DOC_ID:,=!
    set DOC_ID=!DOC_ID: =!
    goto :doc_id_found
)
:doc_id_found

if not "%DOC_ID%"=="" (
    echo ✅ 文档创建成功，ID: %DOC_ID%
) else (
    echo ❌ 创建文档失败，无法获取文档ID
)
echo.

REM 4. 等待文档索引
echo 4️⃣ 等待文档索引完成...
timeout /t 10 /nobreak >nul
echo ✅ 等待完成
echo.

REM 5. 获取知识库详情
echo 5️⃣ 获取知识库详情...
curl -s "%BASE_URL%/knowledge-bases/%KB_ID%"
echo.
echo.

REM 6. 获取文档列表
echo 6️⃣ 获取文档列表...
curl -s "%BASE_URL%/knowledge-bases/%KB_ID%/documents"
echo.
echo.

REM 7. 检索测试
echo 7️⃣ 执行检索测试...
curl -s -X POST "%BASE_URL%/knowledge-bases/%KB_ID%/retrieve" ^
  -H "Content-Type: application/json" ^
  -d "{\"query\": \"什么是人工智能？\", \"topK\": 5, \"scoreThreshold\": 0.5}" > retrieval_response.json

type retrieval_response.json
echo.
echo.

REM 8. 批量检索测试
echo 8️⃣ 执行批量检索测试...
curl -s -X POST "%BASE_URL%/knowledge-bases/%KB_ID%/test-retrieval" ^
  -H "Content-Type: application/json" ^
  -d "{\"questions\": [{\"question\": \"什么是人工智能？\", \"expectedAnswer\": \"人工智能是计算机科学的一个分支\"}, {\"question\": \"什么是机器学习？\", \"expectedAnswer\": \"机器学习是人工智能的一个子集\"}]}" > batch_test_response.json

type batch_test_response.json
echo.
echo.

REM 9. 获取文档分段（如果文档创建成功）
if not "%DOC_ID%"=="" (
    echo 9️⃣ 获取文档分段...
    curl -s "%BASE_URL%/knowledge-bases/%KB_ID%/documents/%DOC_ID%/segments"
    echo.
    echo.
)

REM 10. 清理测试数据
echo 🧹 清理测试数据...
curl -s -X DELETE "%BASE_URL%/knowledge-bases/%KB_ID%" > delete_response.json
type delete_response.json
echo.

findstr "\"success\":true" delete_response.json >nul
if not errorlevel 1 (
    echo ✅ 知识库删除成功
) else (
    echo ⚠️ 知识库删除失败，请手动清理: %KB_ID%
)
echo.

echo 🎉 Dify 知识库 API 测试完成！
echo.
echo 📊 测试总结:
if not "%KB_ID%"=="" (
    echo - 知识库创建: ✅ 成功
) else (
    echo - 知识库创建: ❌ 失败
)

if not "%DOC_ID%"=="" (
    echo - 文档创建: ✅ 成功
) else (
    echo - 文档创建: ❌ 失败
)

findstr "\"success\":true" retrieval_response.json >nul
if not errorlevel 1 (
    echo - 检索功能: ✅ 成功
) else (
    echo - 检索功能: ❌ 失败
)

findstr "\"success\":true" batch_test_response.json >nul
if not errorlevel 1 (
    echo - 批量测试: ✅ 成功
) else (
    echo - 批量测试: ❌ 失败
)

echo.
echo 如果测试失败，请检查:
echo 1. Dify 服务是否正常运行
echo 2. API 配置是否正确
echo 3. 网络连接是否正常
echo 4. API 密钥是否有效
echo.

REM 清理临时文件
del /q kb_response.json doc_response.json retrieval_response.json batch_test_response.json delete_response.json 2>nul

pause
