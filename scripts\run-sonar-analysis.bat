@echo off
REM Sonar 代码质量分析脚本 (Windows 版本)
REM 使用方法: scripts\run-sonar-analysis.bat [sonar-server-url] [sonar-token]

setlocal enabledelayedexpansion

REM 默认配置
set DEFAULT_SONAR_HOST_URL=http://localhost:9000
set DEFAULT_SONAR_TOKEN=

REM 获取参数
if "%1"=="" (
    set SONAR_HOST_URL=%DEFAULT_SONAR_HOST_URL%
) else (
    set SONAR_HOST_URL=%1
)

if "%2"=="" (
    set SONAR_TOKEN=%DEFAULT_SONAR_TOKEN%
) else (
    set SONAR_TOKEN=%2
)

echo 🔍 开始 Sonar 代码质量分析
echo ================================
echo Sonar 服务器: %SONAR_HOST_URL%
echo 项目: knowledge-base-testing
echo.

REM 检查 Maven 是否可用
mvn --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Maven 命令
    pause
    exit /b 1
)

REM 检查 Java 版本
echo 📋 检查环境...
java -version
mvn -version
echo.

REM 1. 清理之前的构建
echo 🧹 清理之前的构建...
mvn clean
if errorlevel 1 (
    echo ❌ 清理失败
    pause
    exit /b 1
)
echo.

REM 2. 编译项目
echo 🔨 编译项目...
mvn compile test-compile
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo.

REM 3. 运行测试并生成覆盖率报告
echo 🧪 运行测试并生成覆盖率报告...
mvn test jacoco:report
if errorlevel 1 (
    echo ⚠️ 测试或覆盖率报告生成失败，但继续执行
)
echo.

REM 4. 运行代码质量检查
echo 📊 运行代码质量检查...

REM Checkstyle 检查
echo   - 运行 Checkstyle 检查...
mvn checkstyle:check
if errorlevel 1 (
    echo ⚠️ Checkstyle 检查发现问题，但继续执行
)

REM PMD 检查
echo   - 运行 PMD 检查...
mvn pmd:check
if errorlevel 1 (
    echo ⚠️ PMD 检查发现问题，但继续执行
)

REM SpotBugs 检查
echo   - 运行 SpotBugs 检查...
mvn spotbugs:check
if errorlevel 1 (
    echo ⚠️ SpotBugs 检查发现问题，但继续执行
)

echo.

REM 5. 运行 Sonar 分析
echo 🚀 运行 Sonar 分析...

if not "%SONAR_TOKEN%"=="" (
    mvn sonar:sonar -Dsonar.host.url="%SONAR_HOST_URL%" -Dsonar.login="%SONAR_TOKEN%"
) else (
    echo ⚠️ 未提供 Sonar Token，使用默认认证
    mvn sonar:sonar -Dsonar.host.url="%SONAR_HOST_URL%"
)

if errorlevel 1 (
    echo ❌ Sonar 分析失败
    pause
    exit /b 1
)

echo.

REM 6. 生成报告摘要
echo 📋 生成报告摘要...
echo ================================

REM 检查测试结果
if exist "target\surefire-reports\TEST-*.xml" (
    echo ✅ 测试报告: 找到测试结果文件
) else (
    echo ⚠️ 测试报告: 未找到测试结果文件
)

REM 检查覆盖率报告
if exist "target\site\jacoco\jacoco.xml" (
    echo ✅ 覆盖率报告: target\site\jacoco\jacoco.xml
) else (
    echo ⚠️ 覆盖率报告: 未找到覆盖率报告
)

REM 检查 Checkstyle 报告
if exist "target\checkstyle-result.xml" (
    echo ✅ Checkstyle 报告: target\checkstyle-result.xml
) else (
    echo ⚠️ Checkstyle 报告: 未找到报告文件
)

REM 检查 PMD 报告
if exist "target\pmd.xml" (
    echo ✅ PMD 报告: target\pmd.xml
) else (
    echo ⚠️ PMD 报告: 未找到报告文件
)

REM 检查 SpotBugs 报告
if exist "target\spotbugsXml.xml" (
    echo ✅ SpotBugs 报告: target\spotbugsXml.xml
) else (
    echo ⚠️ SpotBugs 报告: 未找到报告文件
)

echo.
echo 🎉 Sonar 分析完成！
echo.
echo 📊 查看结果:
echo - Sonar 仪表板: %SONAR_HOST_URL%/dashboard?id=knowledge-base-testing
echo - 本地覆盖率报告: target\site\jacoco\index.html
echo - Checkstyle 报告: target\site\checkstyle.html
echo - PMD 报告: target\site\pmd.html
echo - SpotBugs 报告: target\site\spotbugs.html
echo.
echo 💡 提示:
echo 1. 如果是首次运行，请确保 Sonar 服务器已启动
echo 2. 检查 sonar-project.properties 配置是否正确
echo 3. 根据报告修复代码质量问题
echo.

pause
