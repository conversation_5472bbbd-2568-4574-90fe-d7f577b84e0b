package com.kbtesting.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * Swagger/OpenAPI 配置类
 * 
 * <AUTHOR> Testing Team
 */
@Configuration
public class SwaggerConfig {
    
    @Value("${server.port:8080}")
    private String serverPort;
    
    @Value("${server.servlet.context-path:/kb-testing}")
    private String contextPath;
    
    /**
     * 配置 OpenAPI 信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("知识库自动化测试系统 API")
                .description("提供知识库测试、Dify 集成、测试集生成等功能的 REST API 接口")
                .version("1.0.0")
                .contact(new Contact()
                    .name("KB Testing Team")
                    .email("<EMAIL>")
                    .url("https://github.com/kb-testing/knowledge-base-testing"))
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT")))
            .servers(getServers())
            .tags(getTags());
    }
    
    /**
     * 配置服务器信息
     */
    private List<Server> getServers() {
        Server localServer = new Server()
            .url("http://localhost:" + serverPort + contextPath)
            .description("本地开发服务器");
            
        Server devServer = new Server()
            .url("http://dev-server:8080" + contextPath)
            .description("开发环境服务器");
            
        Server prodServer = new Server()
            .url("https://api.kb-testing.com" + contextPath)
            .description("生产环境服务器");
            
        return Arrays.asList(localServer, devServer, prodServer);
    }
    
    /**
     * 配置 API 标签
     */
    private List<Tag> getTags() {
        return Arrays.asList(
            new Tag().name("系统管理").description("系统健康检查、配置管理等基础功能"),
            new Tag().name("知识库测试").description("传统知识库测试相关接口"),
            new Tag().name("Dify 测试").description("Dify 知识库测试相关接口"),
            new Tag().name("测试集管理").description("测试集生成、加载、管理等功能"),
            new Tag().name("报告分析").description("测试报告生成和分析功能")
        );
    }
    
    /**
     * 系统管理 API 分组
     */
    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
            .group("01-系统管理")
            .pathsToMatch("/api/system/**", "/api/health/**")
            .build();
    }
    
    /**
     * 知识库测试 API 分组
     */
    @Bean
    public GroupedOpenApi knowledgeBaseTestApi() {
        return GroupedOpenApi.builder()
            .group("02-知识库测试")
            .pathsToMatch("/api/kb-test/**")
            .build();
    }
    
    /**
     * Dify 测试 API 分组
     */
    @Bean
    public GroupedOpenApi difyTestApi() {
        return GroupedOpenApi.builder()
            .group("03-Dify测试")
            .pathsToMatch("/api/dify-test/**")
            .build();
    }
    
    /**
     * 测试集管理 API 分组
     */
    @Bean
    public GroupedOpenApi testsetApi() {
        return GroupedOpenApi.builder()
            .group("04-测试集管理")
            .pathsToMatch("/api/testset/**")
            .build();
    }
    
    /**
     * 报告分析 API 分组
     */
    @Bean
    public GroupedOpenApi reportApi() {
        return GroupedOpenApi.builder()
            .group("05-报告分析")
            .pathsToMatch("/api/report/**", "/api/analysis/**")
            .build();
    }
    
    /**
     * 所有 API 分组
     */
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
            .group("00-全部接口")
            .pathsToMatch("/api/**")
            .build();
    }
}
