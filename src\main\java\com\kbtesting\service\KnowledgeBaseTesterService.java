package com.kbtesting.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbtesting.config.AppConfig;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.model.TestResult;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 知识库测试服务
 * 
 * <AUTHOR> Testing Team
 */
@Service
public class KnowledgeBaseTesterService {
    
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeBaseTesterService.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    private OkHttpClient httpClient;
    private ExecutorService executorService;

    public KnowledgeBaseTesterService() {
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();
    }

    /**
     * 初始化线程池
     */
    private ExecutorService getExecutorService() {
        if (executorService == null || executorService.isShutdown()) {
            int parallelWorkers = appConfig.getKnowledgeBaseTesting().getParallelWorkers();
            executorService = Executors.newFixedThreadPool(parallelWorkers);
        }
        return executorService;
    }

    /**
     * 运行测试集测试
     */
    public List<TestResult> runTest(String testsetPath, boolean useAsync) {
        logger.info("开始运行测试: {}", testsetPath);
        
        try {
            // 加载测试集
            List<TestQuestion> testQuestions = testsetGeneratorService.loadTestset(testsetPath);
            logger.info("加载了 {} 个测试问题", testQuestions.size());
            
            if (testQuestions.isEmpty()) {
                logger.warn("测试集为空");
                return new ArrayList<>();
            }
            
            // 执行测试
            List<TestResult> results;
            if (useAsync) {
                results = runTestAsync(testQuestions);
            } else {
                results = runTestSync(testQuestions);
            }
            
            logger.info("测试完成，共 {} 个结果", results.size());
            return results;
            
        } catch (Exception e) {
            logger.error("运行测试失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 同步执行测试
     */
    private List<TestResult> runTestSync(List<TestQuestion> testQuestions) {
        List<TestResult> results = new ArrayList<>();
        
        for (int i = 0; i < testQuestions.size(); i++) {
            TestQuestion question = testQuestions.get(i);
            logger.info("执行测试 {}/{}: {}", i + 1, testQuestions.size(), 
                       question.getQuestion().substring(0, Math.min(50, question.getQuestion().length())));
            
            TestResult result = testSingleQuestion(question);
            results.add(result);
        }
        
        return results;
    }

    /**
     * 异步执行测试
     */
    private List<TestResult> runTestAsync(List<TestQuestion> testQuestions) {
        List<TestResult> results = new ArrayList<>();
        int batchSize = appConfig.getKnowledgeBaseTesting().getBatchSize();
        ExecutorService executor = getExecutorService();
        
        // 分批处理
        for (int i = 0; i < testQuestions.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, testQuestions.size());
            List<TestQuestion> batch = testQuestions.subList(i, endIndex);
            
            logger.info("处理批次 {}-{}/{}", i + 1, endIndex, testQuestions.size());
            
            List<Future<TestResult>> futures = new ArrayList<>();
            
            // 提交批次任务
            for (TestQuestion question : batch) {
                Future<TestResult> future = executor.submit(() -> testSingleQuestion(question));
                futures.add(future);
            }
            
            // 收集结果
            for (Future<TestResult> future : futures) {
                try {
                    TestResult result = future.get(60, TimeUnit.SECONDS);
                    results.add(result);
                } catch (Exception e) {
                    logger.error("获取异步测试结果失败: {}", e.getMessage());
                }
            }
        }
        
        return results;
    }

    /**
     * 测试单个问题
     */
    private TestResult testSingleQuestion(TestQuestion testQuestion) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 调用知识库API
            KnowledgeBaseResponse response = queryKnowledgeBase(testQuestion.getQuestion());
            long responseTime = System.currentTimeMillis() - startTime;
            
            if (response.isSuccess()) {
                return new TestResult(
                    testQuestion.getQuestion(),
                    testQuestion.getExpectedAnswer(),
                    response.getAnswer(),
                    responseTime,
                    true
                );
            } else {
                return new TestResult(
                    testQuestion.getQuestion(),
                    testQuestion.getExpectedAnswer(),
                    "",
                    responseTime,
                    false,
                    response.getErrorMessage()
                );
            }
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            logger.error("测试问题失败: {}", e.getMessage());
            
            return new TestResult(
                testQuestion.getQuestion(),
                testQuestion.getExpectedAnswer(),
                "",
                responseTime,
                false,
                e.getMessage()
            );
        }
    }

    /**
     * 查询知识库
     */
    private KnowledgeBaseResponse queryKnowledgeBase(String question) {
        AppConfig.Api.KnowledgeBase kbConfig = appConfig.getApi().getKnowledgeBase();
        Map<String, Object> queryParams = appConfig.getKnowledgeBaseTesting().getQueryParams();
        
        try {
            // 构建请求体
            Map<String, Object> requestBody = Map.of(
                "question", question,
                "top_k", queryParams.getOrDefault("topK", 5),
                "score_threshold", queryParams.getOrDefault("scoreThreshold", 0.7),
                "max_tokens", queryParams.getOrDefault("maxTokens", 1000)
            );
            
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            
            Request request = new Request.Builder()
                .url(kbConfig.getBaseUrl() + "/query")
                .addHeader("Authorization", "Bearer " + kbConfig.getApiKey())
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(jsonBody, MediaType.get("application/json")))
                .build();
            
            // 执行请求（带重试机制）
            return executeWithRetry(request);
            
        } catch (Exception e) {
            logger.error("查询知识库失败: {}", e.getMessage());
            return new KnowledgeBaseResponse(false, "", e.getMessage());
        }
    }

    /**
     * 带重试机制的请求执行
     */
    private KnowledgeBaseResponse executeWithRetry(Request request) {
        int maxRetries = appConfig.getKnowledgeBaseTesting().getMaxRetries();
        int retryDelay = appConfig.getKnowledgeBaseTesting().getRetryDelay();
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    
                    String answer = jsonNode.path("answer").asText();
                    return new KnowledgeBaseResponse(true, answer, null);
                    
                } else {
                    String errorMsg = "HTTP " + response.code() + ": " + response.message();
                    
                    if (attempt < maxRetries) {
                        logger.warn("请求失败，第 {} 次重试: {}", attempt, errorMsg);
                        Thread.sleep(retryDelay);
                        continue;
                    } else {
                        return new KnowledgeBaseResponse(false, "", errorMsg);
                    }
                }
                
            } catch (Exception e) {
                if (attempt < maxRetries) {
                    logger.warn("请求异常，第 {} 次重试: {}", attempt, e.getMessage());
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    return new KnowledgeBaseResponse(false, "", e.getMessage());
                }
            }
        }
        
        return new KnowledgeBaseResponse(false, "", "重试次数已用完");
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 知识库响应内部类
     */
    private static class KnowledgeBaseResponse {
        private final boolean success;
        private final String answer;
        private final String errorMessage;

        public KnowledgeBaseResponse(boolean success, String answer, String errorMessage) {
            this.success = success;
            this.answer = answer;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() { return success; }
        public String getAnswer() { return answer; }
        public String getErrorMessage() { return errorMessage; }
    }
}
