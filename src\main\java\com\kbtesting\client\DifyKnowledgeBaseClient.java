package com.kbtesting.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbtesting.config.AppConfig;
import com.kbtesting.model.dify.*;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Dify 知识库 API 客户端
 * 
 * <AUTHOR> Testing Team
 */
@Component
public class DifyKnowledgeBaseClient {
    
    private static final Logger logger = LoggerFactory.getLogger(DifyKnowledgeBaseClient.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private OkHttpClient httpClient;
    
    public DifyKnowledgeBaseClient() {
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();
    }
    
    /**
     * 创建空知识库
     */
    public DifyKnowledgeBase createKnowledgeBase(String name, String description) throws IOException {
        String url = getBaseUrl() + "/datasets";
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", name);
        if (description != null) {
            requestBody.put("description", description);
        }
        
        String jsonBody = objectMapper.writeValueAsString(requestBody);
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(jsonBody, MediaType.get("application/json")))
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                return objectMapper.treeToValue(jsonNode, DifyKnowledgeBase.class);
            } else {
                throw new IOException("创建知识库失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 获取知识库列表
     */
    public List<DifyKnowledgeBase> getKnowledgeBases(int page, int limit) throws IOException {
        String url = getBaseUrl() + "/datasets?page=" + page + "&limit=" + limit;
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .get()
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode dataNode = jsonNode.path("data");
                
                List<DifyKnowledgeBase> knowledgeBases = new ArrayList<>();
                if (dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        knowledgeBases.add(objectMapper.treeToValue(item, DifyKnowledgeBase.class));
                    }
                }
                return knowledgeBases;
            } else {
                throw new IOException("获取知识库列表失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 查看知识库详情
     */
    public DifyKnowledgeBase getKnowledgeBase(String datasetId) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId;
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .get()
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                return objectMapper.treeToValue(jsonNode, DifyKnowledgeBase.class);
            } else {
                throw new IOException("获取知识库详情失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 修改知识库详情
     */
    public DifyKnowledgeBase updateKnowledgeBase(String datasetId, String name, String description) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId;
        
        Map<String, Object> requestBody = new HashMap<>();
        if (name != null) {
            requestBody.put("name", name);
        }
        if (description != null) {
            requestBody.put("description", description);
        }
        
        String jsonBody = objectMapper.writeValueAsString(requestBody);
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .addHeader("Content-Type", "application/json")
            .patch(RequestBody.create(jsonBody, MediaType.get("application/json")))
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                return objectMapper.treeToValue(jsonNode, DifyKnowledgeBase.class);
            } else {
                throw new IOException("修改知识库失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 删除知识库
     */
    public boolean deleteKnowledgeBase(String datasetId) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId;
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .delete()
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            return response.isSuccessful();
        }
    }
    
    /**
     * 通过文件创建文档
     */
    public DifyDocument createDocumentByFile(String datasetId, File file, String indexingTechnique, 
                                           String processRule) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/document/create_by_file";
        
        MultipartBody.Builder builder = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("data", file.getName(),
                RequestBody.create(file, MediaType.parse("application/octet-stream")));
        
        if (indexingTechnique != null) {
            builder.addFormDataPart("indexing_technique", indexingTechnique);
        }
        if (processRule != null) {
            builder.addFormDataPart("process_rule", processRule);
        }
        
        RequestBody requestBody = builder.build();
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .post(requestBody)
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode documentNode = jsonNode.path("document");
                return objectMapper.treeToValue(documentNode, DifyDocument.class);
            } else {
                throw new IOException("通过文件创建文档失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 通过文本创建文档
     */
    public DifyDocument createDocumentByText(String datasetId, String name, String text, 
                                           String indexingTechnique, String processRule) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/document/create_by_text";
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", name);
        requestBody.put("text", text);
        if (indexingTechnique != null) {
            requestBody.put("indexing_technique", indexingTechnique);
        }
        if (processRule != null) {
            requestBody.put("process_rule", processRule);
        }
        
        String jsonBody = objectMapper.writeValueAsString(requestBody);
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(jsonBody, MediaType.get("application/json")))
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode documentNode = jsonNode.path("document");
                return objectMapper.treeToValue(documentNode, DifyDocument.class);
            } else {
                throw new IOException("通过文本创建文档失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 获取知识库文档列表
     */
    public List<DifyDocument> getDocuments(String datasetId, int page, int limit) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents?page=" + page + "&limit=" + limit;
        
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .get()
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode dataNode = jsonNode.path("data");
                
                List<DifyDocument> documents = new ArrayList<>();
                if (dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        documents.add(objectMapper.treeToValue(item, DifyDocument.class));
                    }
                }
                return documents;
            } else {
                throw new IOException("获取文档列表失败: " + response.code() + " " + response.message());
            }
        }
    }
    
    /**
     * 删除文档
     */
    public boolean deleteDocument(String datasetId, String documentId) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents/" + documentId;

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .delete()
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            return response.isSuccessful();
        }
    }

    /**
     * 获取文档嵌入状态（进度）
     */
    public Map<String, Object> getDocumentEmbeddingStatus(String datasetId, String batchId) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents/" + batchId + "/indexing-status";

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .get()
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                return objectMapper.convertValue(jsonNode, Map.class);
            } else {
                throw new IOException("获取文档嵌入状态失败: " + response.code() + " " + response.message());
            }
        }
    }

    /**
     * 检索知识库
     */
    public DifyRetrievalResult retrieveKnowledgeBase(String datasetId, String query,
                                                   Integer topK, Double scoreThreshold) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/retrieve";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("query", query);
        if (topK != null) {
            requestBody.put("top_k", topK);
        }
        if (scoreThreshold != null) {
            requestBody.put("score_threshold", scoreThreshold);
        }

        String jsonBody = objectMapper.writeValueAsString(requestBody);

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(jsonBody, MediaType.get("application/json")))
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                return objectMapper.treeToValue(jsonNode, DifyRetrievalResult.class);
            } else {
                throw new IOException("检索知识库失败: " + response.code() + " " + response.message());
            }
        }
    }

    /**
     * 查询文档分段
     */
    public List<DifySegment> getDocumentSegments(String datasetId, String documentId,
                                               int page, int limit) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents/" + documentId +
                    "/segments?page=" + page + "&limit=" + limit;

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .get()
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode dataNode = jsonNode.path("data");

                List<DifySegment> segments = new ArrayList<>();
                if (dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        segments.add(objectMapper.treeToValue(item, DifySegment.class));
                    }
                }
                return segments;
            } else {
                throw new IOException("获取文档分段失败: " + response.code() + " " + response.message());
            }
        }
    }

    /**
     * 新增分段
     */
    public DifySegment createSegment(String datasetId, String documentId, String content,
                                   String keywords) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents/" + documentId + "/segments";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("content", content);
        if (keywords != null) {
            requestBody.put("keywords", keywords);
        }

        String jsonBody = objectMapper.writeValueAsString(requestBody);

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(jsonBody, MediaType.get("application/json")))
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode dataNode = jsonNode.path("data");
                return objectMapper.treeToValue(dataNode, DifySegment.class);
            } else {
                throw new IOException("新增分段失败: " + response.code() + " " + response.message());
            }
        }
    }

    /**
     * 更新文档分段
     */
    public DifySegment updateSegment(String datasetId, String documentId, String segmentId,
                                   String content, String keywords) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents/" + documentId +
                    "/segments/" + segmentId;

        Map<String, Object> requestBody = new HashMap<>();
        if (content != null) {
            requestBody.put("content", content);
        }
        if (keywords != null) {
            requestBody.put("keywords", keywords);
        }

        String jsonBody = objectMapper.writeValueAsString(requestBody);

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .addHeader("Content-Type", "application/json")
            .patch(RequestBody.create(jsonBody, MediaType.get("application/json")))
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                JsonNode dataNode = jsonNode.path("data");
                return objectMapper.treeToValue(dataNode, DifySegment.class);
            } else {
                throw new IOException("更新分段失败: " + response.code() + " " + response.message());
            }
        }
    }

    /**
     * 删除文档分段
     */
    public boolean deleteSegment(String datasetId, String documentId, String segmentId) throws IOException {
        String url = getBaseUrl() + "/datasets/" + datasetId + "/documents/" + documentId +
                    "/segments/" + segmentId;

        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + getApiKey())
            .delete()
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            return response.isSuccessful();
        }
    }

    private String getBaseUrl() {
        return appConfig.getApi().getDify().getBaseUrl();
    }

    private String getApiKey() {
        return appConfig.getApi().getDify().getApiKey();
    }
}
