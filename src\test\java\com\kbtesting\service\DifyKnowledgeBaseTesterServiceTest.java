package com.kbtesting.service;

import com.kbtesting.client.DifyKnowledgeBaseClient;
import com.kbtesting.config.AppConfig;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.model.TestResult;
import com.kbtesting.model.dify.DifyKnowledgeBase;
import com.kbtesting.model.dify.DifyDocument;
import com.kbtesting.model.dify.DifyRetrievalResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Dify 知识库测试服务单元测试
 * 
 * <AUTHOR> Testing Team
 */
@ExtendWith(MockitoExtension.class)
class DifyKnowledgeBaseTesterServiceTest {
    
    @Mock
    private DifyKnowledgeBaseClient difyClient;
    
    @Mock
    private AppConfig appConfig;
    
    @Mock
    private TestsetGeneratorService testsetGeneratorService;
    
    @Mock
    private DashScopeService dashScopeService;
    
    @InjectMocks
    private DifyKnowledgeBaseTesterService difyTesterService;
    
    private AppConfig.DifyTesting difyTestingConfig;
    private AppConfig.DifyTesting.KnowledgeBaseConfig knowledgeBaseConfig;
    private AppConfig.DifyTesting.TestSettings testSettings;
    private AppConfig.DifyTesting.Retrieval retrievalConfig;
    
    @BeforeEach
    void setUp() {
        // 设置配置对象
        difyTestingConfig = new AppConfig.DifyTesting();
        knowledgeBaseConfig = new AppConfig.DifyTesting.KnowledgeBaseConfig();
        testSettings = new AppConfig.DifyTesting.TestSettings();
        retrievalConfig = new AppConfig.DifyTesting.Retrieval();
        
        knowledgeBaseConfig.setDefaultIndexingTechnique("high_quality");
        testSettings.setWaitForIndexing(false); // 测试时不等待索引
        testSettings.setCleanupAfterTest(false); // 测试时不清理
        retrievalConfig.setDefaultTopK(5);
        retrievalConfig.setDefaultScoreThreshold(0.7);
        
        difyTestingConfig.setKnowledgeBase(knowledgeBaseConfig);
        difyTestingConfig.setTestSettings(testSettings);
        difyTestingConfig.setRetrieval(retrievalConfig);
        
        when(appConfig.getDifyTesting()).thenReturn(difyTestingConfig);
    }
    
    @Test
    void testCreateTestKnowledgeBase() throws IOException {
        // 准备测试数据
        String name = "测试知识库";
        String description = "这是一个测试知识库";
        
        DifyKnowledgeBase expectedKnowledgeBase = new DifyKnowledgeBase();
        expectedKnowledgeBase.setId("kb-123");
        expectedKnowledgeBase.setName(name);
        expectedKnowledgeBase.setDescription(description);
        
        // 模拟客户端调用
        when(difyClient.createKnowledgeBase(name, description)).thenReturn(expectedKnowledgeBase);
        
        // 执行测试
        DifyKnowledgeBase result = difyTesterService.createTestKnowledgeBase(name, description);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("kb-123", result.getId());
        assertEquals(name, result.getName());
        assertEquals(description, result.getDescription());
        
        // 验证方法调用
        verify(difyClient, times(1)).createKnowledgeBase(name, description);
    }
    
    @Test
    void testCreateTestKnowledgeBaseFailure() throws IOException {
        // 准备测试数据
        String name = "测试知识库";
        String description = "这是一个测试知识库";
        
        // 模拟客户端抛出异常
        when(difyClient.createKnowledgeBase(name, description))
            .thenThrow(new IOException("网络连接失败"));
        
        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            difyTesterService.createTestKnowledgeBase(name, description);
        });
        
        assertEquals("创建知识库失败", exception.getMessage());
        assertTrue(exception.getCause() instanceof IOException);
    }
    
    @Test
    void testCreateTestDocumentByText() throws IOException {
        // 准备测试数据
        String datasetId = "kb-123";
        String name = "测试文档";
        String text = "这是测试文档的内容";
        
        DifyDocument expectedDocument = new DifyDocument();
        expectedDocument.setId("doc-456");
        expectedDocument.setName(name);
        
        // 模拟客户端调用
        when(difyClient.createDocumentByText(eq(datasetId), eq(name), eq(text), 
                                           eq("high_quality"), isNull()))
            .thenReturn(expectedDocument);
        
        // 执行测试
        DifyDocument result = difyTesterService.createTestDocumentByText(datasetId, name, text);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("doc-456", result.getId());
        assertEquals(name, result.getName());
        
        // 验证方法调用
        verify(difyClient, times(1)).createDocumentByText(datasetId, name, text, "high_quality", null);
    }
    
    @Test
    void testRunRetrievalTest() throws IOException {
        // 准备测试数据
        String datasetId = "kb-123";
        List<TestQuestion> testQuestions = Arrays.asList(
            new TestQuestion("什么是人工智能？", "人工智能是计算机科学的一个分支"),
            new TestQuestion("机器学习的定义是什么？", "机器学习是人工智能的一个子集")
        );
        
        // 模拟检索结果
        DifyRetrievalResult.RetrievalRecord record1 = new DifyRetrievalResult.RetrievalRecord();
        record1.setContent("人工智能是计算机科学的一个重要分支");
        record1.setScore(0.9);
        
        DifyRetrievalResult.RetrievalRecord record2 = new DifyRetrievalResult.RetrievalRecord();
        record2.setContent("机器学习是人工智能的核心技术之一");
        record2.setScore(0.85);
        
        DifyRetrievalResult retrievalResult1 = new DifyRetrievalResult();
        retrievalResult1.setQuery("什么是人工智能？");
        retrievalResult1.setRecords(Arrays.asList(record1));
        
        DifyRetrievalResult retrievalResult2 = new DifyRetrievalResult();
        retrievalResult2.setQuery("机器学习的定义是什么？");
        retrievalResult2.setRecords(Arrays.asList(record2));
        
        // 模拟客户端调用
        when(difyClient.retrieveKnowledgeBase(eq(datasetId), eq("什么是人工智能？"), eq(5), eq(0.7)))
            .thenReturn(retrievalResult1);
        when(difyClient.retrieveKnowledgeBase(eq(datasetId), eq("机器学习的定义是什么？"), eq(5), eq(0.7)))
            .thenReturn(retrievalResult2);
        
        // 执行测试
        List<TestResult> results = difyTesterService.runRetrievalTest(datasetId, testQuestions);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        
        TestResult result1 = results.get(0);
        assertTrue(result1.isSuccess());
        assertEquals("什么是人工智能？", result1.getQuestion());
        assertNotNull(result1.getActualAnswer());
        assertTrue(result1.getResponseTime() >= 0);
        
        TestResult result2 = results.get(1);
        assertTrue(result2.isSuccess());
        assertEquals("机器学习的定义是什么？", result2.getQuestion());
        assertNotNull(result2.getActualAnswer());
        assertTrue(result2.getResponseTime() >= 0);
        
        // 验证方法调用
        verify(difyClient, times(2)).retrieveKnowledgeBase(anyString(), anyString(), anyInt(), anyDouble());
    }
    
    @Test
    void testRunCompleteKnowledgeBaseTest() throws IOException {
        // 准备测试数据
        String testName = "完整测试";
        List<File> testDocuments = Arrays.asList(new File("test1.txt"), new File("test2.txt"));
        List<TestQuestion> testQuestions = Arrays.asList(
            new TestQuestion("测试问题1", "测试答案1")
        );
        
        // 模拟知识库创建
        DifyKnowledgeBase knowledgeBase = new DifyKnowledgeBase();
        knowledgeBase.setId("kb-test");
        knowledgeBase.setName(testName);
        
        // 模拟文档上传
        DifyDocument document = new DifyDocument();
        document.setId("doc-test");
        document.setName("test1.txt");
        
        // 模拟检索结果
        DifyRetrievalResult.RetrievalRecord record = new DifyRetrievalResult.RetrievalRecord();
        record.setContent("测试内容");
        record.setScore(0.8);
        
        DifyRetrievalResult retrievalResult = new DifyRetrievalResult();
        retrievalResult.setQuery("测试问题1");
        retrievalResult.setRecords(Arrays.asList(record));
        
        // 模拟客户端调用
        when(difyClient.createKnowledgeBase(anyString(), anyString())).thenReturn(knowledgeBase);
        when(difyClient.createDocumentByFile(anyString(), any(File.class), anyString(), isNull()))
            .thenReturn(document);
        when(difyClient.retrieveKnowledgeBase(anyString(), anyString(), anyInt(), anyDouble()))
            .thenReturn(retrievalResult);
        
        // 执行测试
        Map<String, Object> testReport = difyTesterService.runCompleteKnowledgeBaseTest(
            testName, testDocuments, testQuestions);
        
        // 验证结果
        assertNotNull(testReport);
        assertEquals(testName, testReport.get("testName"));
        assertEquals("kb-test", testReport.get("knowledgeBaseId"));
        assertEquals(2, testReport.get("uploadedDocuments"));
        assertTrue((Boolean) testReport.get("success"));
        assertNotNull(testReport.get("testResults"));
        assertNotNull(testReport.get("statistics"));
        assertNotNull(testReport.get("startTime"));
        assertNotNull(testReport.get("endTime"));
        
        // 验证统计信息
        @SuppressWarnings("unchecked")
        Map<String, Object> statistics = (Map<String, Object>) testReport.get("statistics");
        assertEquals(1, statistics.get("totalTests"));
        assertEquals(1, statistics.get("successfulTests"));
        assertEquals(0, statistics.get("failedTests"));
        assertEquals(1.0, statistics.get("successRate"));
    }
    
    @Test
    void testRunRetrievalTestWithEmptyResults() throws IOException {
        // 准备测试数据
        String datasetId = "kb-123";
        List<TestQuestion> testQuestions = Arrays.asList(
            new TestQuestion("找不到答案的问题", "期望答案")
        );
        
        // 模拟空的检索结果
        DifyRetrievalResult retrievalResult = new DifyRetrievalResult();
        retrievalResult.setQuery("找不到答案的问题");
        retrievalResult.setRecords(Arrays.asList()); // 空结果
        
        // 模拟客户端调用
        when(difyClient.retrieveKnowledgeBase(anyString(), anyString(), anyInt(), anyDouble()))
            .thenReturn(retrievalResult);
        
        // 执行测试
        List<TestResult> results = difyTesterService.runRetrievalTest(datasetId, testQuestions);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        
        TestResult result = results.get(0);
        assertTrue(result.isSuccess());
        assertEquals("找不到答案的问题", result.getQuestion());
        assertEquals("未找到相关内容", result.getActualAnswer());
    }

    @Test
    void testGenerateTestsetFromFile() {
        // 准备测试数据
        String filePath = "data/test-documents/sample.txt";
        List<TestQuestion> expectedQuestions = Arrays.asList(
            new TestQuestion("什么是测试？", "测试是验证软件功能的过程", "easy"),
            new TestQuestion("如何进行单元测试？", "使用JUnit框架编写测试用例", "medium")
        );

        // 模拟服务调用
        when(testsetGeneratorService.generateTestset(filePath)).thenReturn(expectedQuestions);

        // 执行测试
        List<TestQuestion> result = testsetGeneratorService.generateTestset(filePath);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("什么是测试？", result.get(0).getQuestion());
        assertEquals("测试是验证软件功能的过程", result.get(0).getExpectedAnswer());
        assertEquals("easy", result.get(0).getDifficulty());

        // 验证方法调用
        verify(testsetGeneratorService, times(1)).generateTestset(filePath);
    }
}
