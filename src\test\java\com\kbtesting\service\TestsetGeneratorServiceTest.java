package com.kbtesting.service;

import com.kbtesting.config.AppConfig;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.util.DocumentParser;
import com.kbtesting.util.TextSplitter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 测试集生成服务单元测试
 *
 * <AUTHOR> Testing Team
 */
@ExtendWith(MockitoExtension.class)
class TestsetGeneratorServiceTest {

    @Mock
    private AppConfig appConfig;

    @Mock
    private DashScopeService dashScopeService;

    @Mock
    private DocumentParser documentParser;

    @Mock
    private TextSplitter textSplitter;

    @InjectMocks
    private TestsetGeneratorService testsetGeneratorService;

    private AppConfig.TestsetGeneration testsetConfig;
    private AppConfig.QualityControl qualityConfig;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // 设置配置对象
        testsetConfig = new AppConfig.TestsetGeneration();
        testsetConfig.setChunkSize(1000);
        testsetConfig.setChunkOverlap(200);
        testsetConfig.setQuestionsPerChunk(3);
        testsetConfig.setSupportedFormats(Arrays.asList(".txt", ".pdf", ".docx"));
        testsetConfig.setQuestionPromptTemplate(
            "请根据以下文本内容生成{numQuestions}个高质量的问答对：\n{textChunk}\n" +
            "请按以下格式返回：\nQ1: [问题1]\nA1: [答案1]\nQ2: [问题2]\nA2: [答案2]"
        );

        qualityConfig = new AppConfig.QualityControl();
        qualityConfig.setMinQuestionLength(10);
        qualityConfig.setMaxQuestionLength(200);
        qualityConfig.setMinAnswerLength(5);
        qualityConfig.setMaxAnswerLength(500);

        when(appConfig.getTestsetGeneration()).thenReturn(testsetConfig);
        when(appConfig.getQualityControl()).thenReturn(qualityConfig);
    }

    @Test
    void testGenerateTestsetSuccess() throws IOException {
        // 创建临时测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "这是一个测试文档的内容。它包含了关于人工智能的信息。".getBytes());

        // 模拟依赖服务的行为
        when(documentParser.extractText(testFile.toString()))
            .thenReturn("这是一个测试文档的内容。它包含了关于人工智能的信息。");

        when(textSplitter.splitText(anyString(), eq(1000), eq(200)))
            .thenReturn(Arrays.asList("这是一个测试文档的内容。它包含了关于人工智能的信息。"));

        when(dashScopeService.generateText(anyString()))
            .thenReturn("Q1: 什么是人工智能？\nA1: 人工智能是计算机科学的一个分支\nQ2: 测试的目的是什么？\nA2: 测试是为了验证软件功能");

        // 执行测试
        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        TestQuestion question1 = result.get(0);
        assertEquals("什么是人工智能？", question1.getQuestion());
        assertEquals("人工智能是计算机科学的一个分支", question1.getExpectedAnswer());
        assertNotNull(question1.getDifficulty());

        TestQuestion question2 = result.get(1);
        assertEquals("测试的目的是什么？", question2.getQuestion());
        assertEquals("测试是为了验证软件功能", question2.getExpectedAnswer());

        // 验证方法调用
        verify(documentParser, times(1)).extractText(testFile.toString());
        verify(textSplitter, times(1)).splitText(anyString(), eq(1000), eq(200));
        verify(dashScopeService, times(1)).generateText(anyString());
    }

    @Test
    void testGenerateTestsetFileNotExists() {
        // 测试文件不存在的情况
        String nonExistentFile = tempDir.resolve("nonexistent.txt").toString();

        List<TestQuestion> result = testsetGeneratorService.generateTestset(nonExistentFile);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用其他服务
        verify(documentParser, never()).extractText(anyString());
        verify(textSplitter, never()).splitText(anyString(), anyInt(), anyInt());
        verify(dashScopeService, never()).generateText(anyString());
    }

    @Test
    void testGenerateTestsetUnsupportedFormat() throws IOException {
        // 创建不支持格式的文件
        Path testFile = tempDir.resolve("test.xyz");
        Files.write(testFile, "测试内容".getBytes());

        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用其他服务
        verify(documentParser, never()).extractText(anyString());
    }

    @Test
    void testGenerateTestsetEmptyText() throws IOException {
        // 创建临时测试文件
        Path testFile = tempDir.resolve("empty.txt");
        Files.write(testFile, "".getBytes());

        // 模拟文档解析器返回空文本
        when(documentParser.extractText(testFile.toString())).thenReturn("");

        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用了文档解析器但没有调用其他服务
        verify(documentParser, times(1)).extractText(testFile.toString());
        verify(textSplitter, never()).splitText(anyString(), anyInt(), anyInt());
        verify(dashScopeService, never()).generateText(anyString());
    }

    @Test
    void testGenerateTestsetWithException() throws IOException {
        // 创建临时测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "测试内容".getBytes());

        // 模拟文档解析器抛出异常
        when(documentParser.extractText(testFile.toString()))
            .thenThrow(new RuntimeException("解析文档失败"));

        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(documentParser, times(1)).extractText(testFile.toString());
    }

    @Test
    void testGenerateTestsetWithShortChunks() throws IOException {
        // 创建临时测试文件
        Path testFile = tempDir.resolve("short.txt");
        Files.write(testFile, "短文本".getBytes());

        // 模拟依赖服务的行为
        when(documentParser.extractText(testFile.toString())).thenReturn("短文本");
        when(textSplitter.splitText(anyString(), eq(1000), eq(200)))
            .thenReturn(Arrays.asList("短")); // 返回很短的文本块

        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        assertNotNull(result);
        assertTrue(result.isEmpty()); // 短文本块应该被跳过

        verify(documentParser, times(1)).extractText(testFile.toString());
        verify(textSplitter, times(1)).splitText(anyString(), eq(1000), eq(200));
        verify(dashScopeService, never()).generateText(anyString()); // 不应该调用生成服务
    }

    @Test
    void testGenerateTestsetWithDuplicates() throws IOException {
        // 创建临时测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "测试内容，包含重复信息。测试内容，包含重复信息。".getBytes());

        // 模拟依赖服务的行为
        when(documentParser.extractText(testFile.toString()))
            .thenReturn("测试内容，包含重复信息。测试内容，包含重复信息。");

        when(textSplitter.splitText(anyString(), eq(1000), eq(200)))
            .thenReturn(Arrays.asList(
                "测试内容，包含重复信息。",
                "测试内容，包含重复信息。"
            ));

        // 模拟生成相同的问答对
        when(dashScopeService.generateText(anyString()))
            .thenReturn("Q1: 什么是测试？\nA1: 测试是验证功能的过程");

        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        assertNotNull(result);
        assertEquals(1, result.size()); // 重复的问题应该被去除

        verify(dashScopeService, times(2)).generateText(anyString()); // 应该调用两次
    }

    @Test
    void testGenerateTestsetWithInvalidQuestions() throws IOException {
        // 创建临时测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "这是一个测试文档的内容。".getBytes());

        // 模拟依赖服务的行为
        when(documentParser.extractText(testFile.toString()))
            .thenReturn("这是一个测试文档的内容。");

        when(textSplitter.splitText(anyString(), eq(1000), eq(200)))
            .thenReturn(Arrays.asList("这是一个测试文档的内容。"));

        // 模拟生成不符合质量要求的问答对（问题太短）
        when(dashScopeService.generateText(anyString()))
            .thenReturn("Q1: 啥？\nA1: 不知道");

        List<TestQuestion> result = testsetGeneratorService.generateTestset(testFile.toString());

        assertNotNull(result);
        assertTrue(result.isEmpty()); // 不符合质量要求的问题应该被过滤掉

        verify(dashScopeService, times(1)).generateText(anyString());
    }
}