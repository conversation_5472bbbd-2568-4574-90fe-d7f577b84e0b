package com.kbtesting.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Dify 文档分段模型
 * 
 * <AUTHOR> Testing Team
 */
public class DifySegment {
    
    private String id;
    private Integer position;
    
    @JsonProperty("document_id")
    private String documentId;
    
    private String content;
    
    @JsonProperty("word_count")
    private Integer wordCount;
    
    private String tokens;
    
    private String keywords;
    
    @JsonProperty("index_node_id")
    private String indexNodeId;
    
    @JsonProperty("index_node_hash")
    private String indexNodeHash;
    
    @JsonProperty("hit_count")
    private Integer hitCount;
    
    private Boolean enabled;
    private Boolean disabled;
    
    private String status;
    
    @JsonProperty("created_by")
    private String createdBy;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("indexing_at")
    private LocalDateTime indexingAt;
    
    @JsonProperty("completed_at")
    private LocalDateTime completedAt;
    
    @JsonProperty("error")
    private String error;
    
    @JsonProperty("stopped_at")
    private LocalDateTime stoppedAt;
    
    // 构造函数
    public DifySegment() {}
    
    public DifySegment(String content, String documentId) {
        this.content = content;
        this.documentId = documentId;
    }
    
    // Getter 和 Setter 方法
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public Integer getPosition() { return position; }
    public void setPosition(Integer position) { this.position = position; }
    
    public String getDocumentId() { return documentId; }
    public void setDocumentId(String documentId) { this.documentId = documentId; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public Integer getWordCount() { return wordCount; }
    public void setWordCount(Integer wordCount) { this.wordCount = wordCount; }
    
    public String getTokens() { return tokens; }
    public void setTokens(String tokens) { this.tokens = tokens; }
    
    public String getKeywords() { return keywords; }
    public void setKeywords(String keywords) { this.keywords = keywords; }
    
    public String getIndexNodeId() { return indexNodeId; }
    public void setIndexNodeId(String indexNodeId) { this.indexNodeId = indexNodeId; }
    
    public String getIndexNodeHash() { return indexNodeHash; }
    public void setIndexNodeHash(String indexNodeHash) { this.indexNodeHash = indexNodeHash; }
    
    public Integer getHitCount() { return hitCount; }
    public void setHitCount(Integer hitCount) { this.hitCount = hitCount; }
    
    public Boolean getEnabled() { return enabled; }
    public void setEnabled(Boolean enabled) { this.enabled = enabled; }
    
    public Boolean getDisabled() { return disabled; }
    public void setDisabled(Boolean disabled) { this.disabled = disabled; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public LocalDateTime getIndexingAt() { return indexingAt; }
    public void setIndexingAt(LocalDateTime indexingAt) { this.indexingAt = indexingAt; }
    
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    
    public LocalDateTime getStoppedAt() { return stoppedAt; }
    public void setStoppedAt(LocalDateTime stoppedAt) { this.stoppedAt = stoppedAt; }
    
    @Override
    public String toString() {
        return "DifySegment{" +
                "id='" + id + '\'' +
                ", position=" + position +
                ", documentId='" + documentId + '\'' +
                ", wordCount=" + wordCount +
                ", status='" + status + '\'' +
                '}';
    }
}
