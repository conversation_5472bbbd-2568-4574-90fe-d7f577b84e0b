# Dify 知识库测试指南

本指南介绍如何使用知识库自动化测试系统来测试 Dify 知识库的功能。

## 🚀 快速开始

### 1. 配置 Dify API

编辑 `src/main/resources/application.yml` 文件，配置 Dify API 连接信息：

```yaml
api:
  dify:
    base-url: "http://your-dify-server/v1"  # Dify API 地址
    api-key: "dataset-your_api_key"         # Dify API 密钥
    timeout: 30000
```

### 2. 配置测试参数

```yaml
dify-testing:
  # 知识库创建配置
  knowledge-base:
    default-indexing-technique: "high_quality"  # 索引技术：high_quality 或 economy
    default-embedding-model: "text-embedding-ada-002"
    default-embedding-provider: "openai"
  
  # 文档处理配置
  document-processing:
    chunk-size: 1000
    chunk-overlap: 200
    separator: "\n\n"
    
  # 检索配置
  retrieval:
    default-top-k: 5
    default-score-threshold: 0.7
    search-method: "semantic_search"
    
  # 测试配置
  test-settings:
    cleanup-after-test: true   # 测试后是否清理知识库
    wait-for-indexing: true    # 是否等待文档索引完成
    indexing-timeout: 300      # 索引超时时间（秒）
```

## 📡 REST API 接口

### 基础 URL
```
http://localhost:8080/kb-testing/api/dify-test
```

### 主要接口

#### 1. 健康检查
```http
GET /health
```

#### 2. 知识库管理

**创建知识库**
```http
POST /knowledge-bases
Content-Type: application/json

{
  "name": "测试知识库",
  "description": "这是一个测试知识库"
}
```

**获取知识库列表**
```http
GET /knowledge-bases?page=1&limit=20
```

**获取知识库详情**
```http
GET /knowledge-bases/{datasetId}
```

**删除知识库**
```http
DELETE /knowledge-bases/{datasetId}
```

#### 3. 文档管理

**上传文档**
```http
POST /knowledge-bases/{datasetId}/documents/upload
Content-Type: multipart/form-data

file: [文档文件]
```

**通过文本创建文档**
```http
POST /knowledge-bases/{datasetId}/documents/text
Content-Type: application/json

{
  "name": "文档名称",
  "text": "文档内容"
}
```

**获取文档列表**
```http
GET /knowledge-bases/{datasetId}/documents?page=1&limit=20
```

**删除文档**
```http
DELETE /knowledge-bases/{datasetId}/documents/{documentId}
```

#### 4. 检索测试

**单次检索**
```http
POST /knowledge-bases/{datasetId}/retrieve
Content-Type: application/json

{
  "query": "查询内容",
  "topK": 5,
  "scoreThreshold": 0.7
}
```

**批量检索测试**
```http
POST /knowledge-bases/{datasetId}/test-retrieval
Content-Type: application/json

{
  "questions": [
    {
      "question": "什么是人工智能？",
      "expectedAnswer": "人工智能是计算机科学的一个分支"
    },
    {
      "question": "机器学习的定义是什么？",
      "expectedAnswer": "机器学习是人工智能的一个子集"
    }
  ]
}
```

#### 5. 完整测试流程

**运行完整测试**
```http
POST /run-complete-test
Content-Type: multipart/form-data

testName: 完整测试
files: [测试文档1, 测试文档2]
testsetPath: [可选，测试集文件路径]
```

#### 6. 分段管理

**获取文档分段**
```http
GET /knowledge-bases/{datasetId}/documents/{documentId}/segments?page=1&limit=20
```

**新增分段**
```http
POST /knowledge-bases/{datasetId}/documents/{documentId}/segments
Content-Type: application/json

{
  "content": "分段内容",
  "keywords": "关键词1,关键词2"
}
```

**更新分段**
```http
PUT /knowledge-bases/{datasetId}/documents/{documentId}/segments/{segmentId}
Content-Type: application/json

{
  "content": "更新后的内容",
  "keywords": "新关键词"
}
```

**删除分段**
```http
DELETE /knowledge-bases/{datasetId}/documents/{documentId}/segments/{segmentId}
```

## 🧪 测试用例

### 单元测试

运行 Dify 相关的单元测试：
```bash
mvn test -Dtest=DifyKnowledgeBaseTesterServiceTest
mvn test -Dtest=DifyTestControllerTest
```

### 集成测试

运行集成测试（需要真实的 Dify API 连接）：
```bash
# 设置环境变量启用集成测试
export DIFY_INTEGRATION_TEST=true

# 运行集成测试
mvn test -Dtest=DifyKnowledgeBaseIntegrationTest
```

## 📝 使用示例

### 1. 使用 curl 测试

**创建知识库**
```bash
curl -X POST http://localhost:8080/kb-testing/api/dify-test/knowledge-bases \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试知识库",
    "description": "API测试创建的知识库"
  }'
```

**上传文档**
```bash
curl -X POST http://localhost:8080/kb-testing/api/dify-test/knowledge-bases/kb-123/documents/upload \
  -F "file=@data/test-documents/dify-knowledge-base-sample.txt"
```

**检索测试**
```bash
curl -X POST http://localhost:8080/kb-testing/api/dify-test/knowledge-bases/kb-123/retrieve \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是Dify？",
    "topK": 5,
    "scoreThreshold": 0.7
  }'
```

### 2. 使用 Java 代码

```java
@Autowired
private DifyKnowledgeBaseTesterService difyTesterService;

// 创建知识库
DifyKnowledgeBase kb = difyTesterService.createTestKnowledgeBase(
    "测试知识库", "测试描述");

// 上传文档
File testFile = new File("data/test-documents/sample.txt");
DifyDocument doc = difyTesterService.uploadTestDocument(kb.getId(), testFile);

// 执行检索测试
List<TestQuestion> questions = Arrays.asList(
    new TestQuestion("什么是Dify？", "Dify是开源LLM应用开发平台")
);
List<TestResult> results = difyTesterService.runRetrievalTest(kb.getId(), questions);
```

## 🔧 故障排除

### 常见问题

1. **API 连接失败**
   - 检查 Dify 服务是否正常运行
   - 验证 API 地址和密钥配置
   - 确认网络连接正常

2. **文档上传失败**
   - 检查文档格式是否支持
   - 验证文档大小是否超限
   - 确认 API 权限设置

3. **检索结果为空**
   - 确认文档已完成索引
   - 调整相似度阈值参数
   - 检查查询内容是否合适

4. **测试超时**
   - 增加索引等待时间
   - 调整 HTTP 超时配置
   - 检查服务器性能

### 日志查看

```bash
# 查看应用日志
tail -f logs/application/kb-testing.log

# 查看 Dify 相关日志
grep "Dify" logs/application/kb-testing.log
```

## 📊 测试报告

完整测试会生成详细的测试报告，包含：

- 测试基本信息（名称、时间、知识库ID）
- 文档上传统计
- 检索测试结果
- 性能统计（成功率、平均响应时间）
- 错误信息和建议

示例报告结构：
```json
{
  "testName": "完整测试",
  "startTime": "2024-01-01T10:00:00",
  "endTime": "2024-01-01T10:05:00",
  "knowledgeBaseId": "kb-123",
  "uploadedDocuments": 2,
  "testResults": [...],
  "statistics": {
    "totalTests": 10,
    "successfulTests": 8,
    "failedTests": 2,
    "successRate": 0.8,
    "averageResponseTime": 1250.5
  },
  "success": true
}
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进 Dify 知识库测试功能！

## 📄 许可证

MIT License
