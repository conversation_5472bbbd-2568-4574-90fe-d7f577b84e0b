package com.kbtesting.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Dify 文档模型
 * 
 * <AUTHOR> Testing Team
 */
public class DifyDocument {
    
    private String id;
    private String position;
    
    @JsonProperty("data_source_type")
    private String dataSourceType;
    
    @JsonProperty("data_source_info")
    private DataSourceInfo dataSourceInfo;
    
    @JsonProperty("dataset_process_rule_id")
    private String datasetProcessRuleId;
    
    private String name;
    
    @JsonProperty("created_from")
    private String createdFrom;
    
    @JsonProperty("created_by")
    private String createdBy;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("indexing_status")
    private String indexingStatus;
    
    private Boolean enabled;
    private Boolean disabled;
    private Boolean archived;
    
    @JsonProperty("display_status")
    private String displayStatus;
    
    @JsonProperty("word_count")
    private Integer wordCount;
    
    @JsonProperty("hit_count")
    private Integer hitCount;
    
    @JsonProperty("doc_form")
    private String docForm;
    
    // 构造函数
    public DifyDocument() {}
    
    public DifyDocument(String name, String dataSourceType) {
        this.name = name;
        this.dataSourceType = dataSourceType;
    }
    
    // Getter 和 Setter 方法
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }
    
    public String getDataSourceType() { return dataSourceType; }
    public void setDataSourceType(String dataSourceType) { this.dataSourceType = dataSourceType; }
    
    public DataSourceInfo getDataSourceInfo() { return dataSourceInfo; }
    public void setDataSourceInfo(DataSourceInfo dataSourceInfo) { this.dataSourceInfo = dataSourceInfo; }
    
    public String getDatasetProcessRuleId() { return datasetProcessRuleId; }
    public void setDatasetProcessRuleId(String datasetProcessRuleId) { this.datasetProcessRuleId = datasetProcessRuleId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getCreatedFrom() { return createdFrom; }
    public void setCreatedFrom(String createdFrom) { this.createdFrom = createdFrom; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public String getIndexingStatus() { return indexingStatus; }
    public void setIndexingStatus(String indexingStatus) { this.indexingStatus = indexingStatus; }
    
    public Boolean getEnabled() { return enabled; }
    public void setEnabled(Boolean enabled) { this.enabled = enabled; }
    
    public Boolean getDisabled() { return disabled; }
    public void setDisabled(Boolean disabled) { this.disabled = disabled; }
    
    public Boolean getArchived() { return archived; }
    public void setArchived(Boolean archived) { this.archived = archived; }
    
    public String getDisplayStatus() { return displayStatus; }
    public void setDisplayStatus(String displayStatus) { this.displayStatus = displayStatus; }
    
    public Integer getWordCount() { return wordCount; }
    public void setWordCount(Integer wordCount) { this.wordCount = wordCount; }
    
    public Integer getHitCount() { return hitCount; }
    public void setHitCount(Integer hitCount) { this.hitCount = hitCount; }
    
    public String getDocForm() { return docForm; }
    public void setDocForm(String docForm) { this.docForm = docForm; }
    
    @Override
    public String toString() {
        return "DifyDocument{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", dataSourceType='" + dataSourceType + '\'' +
                ", indexingStatus='" + indexingStatus + '\'' +
                ", wordCount=" + wordCount +
                '}';
    }
    
    /**
     * 数据源信息
     */
    public static class DataSourceInfo {
        @JsonProperty("upload_file_id")
        private String uploadFileId;
        
        @JsonProperty("upload_file_name")
        private String uploadFileName;
        
        @JsonProperty("upload_file_size")
        private Long uploadFileSize;
        
        @JsonProperty("upload_file_extension")
        private String uploadFileExtension;
        
        @JsonProperty("upload_file_mime_type")
        private String uploadFileMimeType;
        
        @JsonProperty("upload_file_url")
        private String uploadFileUrl;
        
        // Getter 和 Setter 方法
        public String getUploadFileId() { return uploadFileId; }
        public void setUploadFileId(String uploadFileId) { this.uploadFileId = uploadFileId; }
        
        public String getUploadFileName() { return uploadFileName; }
        public void setUploadFileName(String uploadFileName) { this.uploadFileName = uploadFileName; }
        
        public Long getUploadFileSize() { return uploadFileSize; }
        public void setUploadFileSize(Long uploadFileSize) { this.uploadFileSize = uploadFileSize; }
        
        public String getUploadFileExtension() { return uploadFileExtension; }
        public void setUploadFileExtension(String uploadFileExtension) { this.uploadFileExtension = uploadFileExtension; }
        
        public String getUploadFileMimeType() { return uploadFileMimeType; }
        public void setUploadFileMimeType(String uploadFileMimeType) { this.uploadFileMimeType = uploadFileMimeType; }
        
        public String getUploadFileUrl() { return uploadFileUrl; }
        public void setUploadFileUrl(String uploadFileUrl) { this.uploadFileUrl = uploadFileUrl; }
    }
}
