package com.kbtesting.controller;

import com.kbtesting.model.TestQuestion;
import com.kbtesting.service.TestsetGeneratorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试集管理控制器
 * 
 * <AUTHOR> Testing Team
 */
@Tag(name = "测试集管理", description = "测试集生成、加载、管理等功能")
@RestController
@RequestMapping("/api/testset")
@CrossOrigin(origins = "*")
public class TestsetController {
    
    private static final Logger logger = LoggerFactory.getLogger(TestsetController.class);
    
    // 常量定义
    private static final String SUCCESS_KEY = "success";
    private static final String ERROR_KEY = "error";
    private static final String MESSAGE_KEY = "message";
    private static final String DATA_KEY = "data";
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    /**
     * 从文件生成测试集
     */
    @Operation(summary = "从文件生成测试集", description = "上传文档文件并自动生成测试问答对")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "生成成功"),
        @ApiResponse(responseCode = "400", description = "文件格式不支持或参数错误"),
        @ApiResponse(responseCode = "500", description = "生成失败")
    })
    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generateTestset(
            @Parameter(description = "文档文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "是否保存到文件", example = "false")
            @RequestParam(value = "saveToFile", defaultValue = "false") boolean saveToFile,
            @Parameter(description = "输出文件名", example = "generated-testset.json")
            @RequestParam(value = "outputFileName", required = false) String outputFileName) {
        
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(ERROR_KEY, "上传文件不能为空"));
            }
            
            // 检查文件格式
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return ResponseEntity.badRequest().body(Map.of(ERROR_KEY, "文件名无效"));
            }
            
            // 保存上传的文件到临时目录
            Path tempDir = Paths.get("temp/testset-generation");
            Files.createDirectories(tempDir);
            
            Path tempFile = tempDir.resolve(originalFilename);
            Files.copy(file.getInputStream(), tempFile);
            
            try {
                // 生成测试集
                List<TestQuestion> testQuestions;
                if (saveToFile && outputFileName != null && !outputFileName.trim().isEmpty()) {
                    // 生成并保存到文件
                    testQuestions = testsetGeneratorService.generateAndSaveTestset(
                        tempFile.toString(), outputFileName);
                } else {
                    // 只生成不保存
                    testQuestions = testsetGeneratorService.generateTestset(tempFile.toString());
                }
                
                // 构建响应
                Map<String, Object> response = new HashMap<>();
                response.put(SUCCESS_KEY, true);
                response.put(DATA_KEY, testQuestions);
                response.put("totalQuestions", testQuestions.size());
                response.put("fileName", originalFilename);
                response.put("generatedAt", LocalDateTime.now());
                
                if (saveToFile && outputFileName != null) {
                    response.put("savedToFile", outputFileName);
                    response.put(MESSAGE_KEY, "测试集生成成功并已保存到文件: " + outputFileName);
                } else {
                    response.put(MESSAGE_KEY, "测试集生成成功");
                }
                
                // 统计信息
                Map<String, Long> difficultyStats = testQuestions.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                        q -> q.getDifficulty() != null ? q.getDifficulty() : "unknown",
                        java.util.stream.Collectors.counting()
                    ));
                response.put("difficultyStats", difficultyStats);
                
                return ResponseEntity.ok(response);
                
            } finally {
                // 清理临时文件
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    logger.warn("清理临时文件失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("生成测试集失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Map.of(ERROR_KEY, "生成测试集失败: " + e.getMessage()));
        }
    }
    
    /**
     * 从文件路径生成测试集
     */
    @Operation(summary = "从文件路径生成测试集", description = "指定服务器上的文件路径生成测试集")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "生成成功"),
        @ApiResponse(responseCode = "400", description = "文件路径无效"),
        @ApiResponse(responseCode = "500", description = "生成失败")
    })
    @PostMapping("/generate-from-path")
    public ResponseEntity<Map<String, Object>> generateTestsetFromPath(
            @Parameter(description = "文件路径", required = true, example = "data/documents/sample.pdf")
            @RequestParam("filePath") String filePath) {
        
        try {
            if (filePath == null || filePath.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(ERROR_KEY, "文件路径不能为空"));
            }
            
            // 生成测试集
            List<TestQuestion> testQuestions = testsetGeneratorService.generateTestset(filePath);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put(SUCCESS_KEY, true);
            response.put(DATA_KEY, testQuestions);
            response.put("totalQuestions", testQuestions.size());
            response.put("filePath", filePath);
            response.put("generatedAt", LocalDateTime.now());
            response.put(MESSAGE_KEY, "测试集生成成功");
            
            // 统计信息
            if (!testQuestions.isEmpty()) {
                Map<String, Long> difficultyStats = testQuestions.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                        q -> q.getDifficulty() != null ? q.getDifficulty() : "unknown",
                        java.util.stream.Collectors.counting()
                    ));
                response.put("difficultyStats", difficultyStats);
                
                double avgQuestionLength = testQuestions.stream()
                    .mapToInt(q -> q.getQuestion().length())
                    .average()
                    .orElse(0.0);
                
                double avgAnswerLength = testQuestions.stream()
                    .mapToInt(q -> q.getExpectedAnswer().length())
                    .average()
                    .orElse(0.0);
                
                Map<String, Object> stats = new HashMap<>();
                stats.put("avgQuestionLength", Math.round(avgQuestionLength * 100.0) / 100.0);
                stats.put("avgAnswerLength", Math.round(avgAnswerLength * 100.0) / 100.0);
                response.put("statistics", stats);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("从路径生成测试集失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Map.of(ERROR_KEY, "生成测试集失败: " + e.getMessage()));
        }
    }
    
    /**
     * 加载测试集文件
     */
    @Operation(summary = "加载测试集文件", description = "从指定路径加载已保存的测试集文件")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "加载成功"),
        @ApiResponse(responseCode = "404", description = "文件不存在"),
        @ApiResponse(responseCode = "500", description = "加载失败")
    })
    @GetMapping("/load")
    public ResponseEntity<Map<String, Object>> loadTestset(
            @Parameter(description = "测试集文件路径", required = true, example = "testsets/sample-testset.json")
            @RequestParam("filePath") String filePath) {
        
        try {
            if (filePath == null || filePath.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(ERROR_KEY, "文件路径不能为空"));
            }
            
            // 加载测试集
            List<TestQuestion> testQuestions = testsetGeneratorService.loadTestset(filePath);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put(SUCCESS_KEY, true);
            response.put(DATA_KEY, testQuestions);
            response.put("totalQuestions", testQuestions.size());
            response.put("filePath", filePath);
            response.put("loadedAt", LocalDateTime.now());
            response.put(MESSAGE_KEY, "测试集加载成功");
            
            // 统计信息
            if (!testQuestions.isEmpty()) {
                Map<String, Long> difficultyStats = testQuestions.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                        q -> q.getDifficulty() != null ? q.getDifficulty() : "unknown",
                        java.util.stream.Collectors.counting()
                    ));
                response.put("difficultyStats", difficultyStats);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("加载测试集失败: {}", e.getMessage(), e);
            if (e.getMessage().contains("不存在") || e.getMessage().contains("not found")) {
                return ResponseEntity.status(404).body(Map.of(ERROR_KEY, "测试集文件不存在: " + filePath));
            } else {
                return ResponseEntity.status(500).body(Map.of(ERROR_KEY, "加载测试集失败: " + e.getMessage()));
            }
        }
    }
    
    /**
     * 验证测试集格式
     */
    @Operation(summary = "验证测试集格式", description = "验证上传的测试集文件格式是否正确")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证成功"),
        @ApiResponse(responseCode = "400", description = "格式错误")
    })
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateTestset(
            @Parameter(description = "测试集文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(ERROR_KEY, "上传文件不能为空"));
            }
            
            // 保存临时文件
            Path tempDir = Paths.get("temp/validation");
            Files.createDirectories(tempDir);
            
            Path tempFile = tempDir.resolve(file.getOriginalFilename());
            Files.copy(file.getInputStream(), tempFile);
            
            try {
                // 尝试加载测试集
                List<TestQuestion> testQuestions = testsetGeneratorService.loadTestset(tempFile.toString());
                
                // 验证成功
                Map<String, Object> response = new HashMap<>();
                response.put(SUCCESS_KEY, true);
                response.put("valid", true);
                response.put("totalQuestions", testQuestions.size());
                response.put(MESSAGE_KEY, "测试集格式验证通过");
                
                // 详细验证信息
                Map<String, Object> validation = new HashMap<>();
                validation.put("hasQuestions", !testQuestions.isEmpty());
                validation.put("allQuestionsValid", testQuestions.stream()
                    .allMatch(q -> q.getQuestion() != null && !q.getQuestion().trim().isEmpty() &&
                                 q.getExpectedAnswer() != null && !q.getExpectedAnswer().trim().isEmpty()));
                
                response.put("validation", validation);
                
                return ResponseEntity.ok(response);
                
            } finally {
                // 清理临时文件
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    logger.warn("清理临时文件失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("验证测试集失败: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put(SUCCESS_KEY, false);
            response.put("valid", false);
            response.put(ERROR_KEY, "测试集格式验证失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
}
