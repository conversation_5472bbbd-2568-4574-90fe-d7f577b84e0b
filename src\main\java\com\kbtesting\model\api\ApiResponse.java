package com.kbtesting.model.api;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 统一 API 响应模型
 * 
 * <AUTHOR> Testing Team
 */
@Schema(description = "统一API响应格式")
public class ApiResponse<T> {
    
    @Schema(description = "请求是否成功", example = "true")
    private boolean success;
    
    @Schema(description = "响应消息", example = "操作成功")
    private String message;
    
    @Schema(description = "响应数据")
    private T data;
    
    @Schema(description = "错误代码", example = "400")
    private Integer errorCode;
    
    @Schema(description = "错误详情", example = "参数验证失败")
    private String errorDetail;
    
    @Schema(description = "响应时间戳", example = "2024-01-01T10:00:00")
    private LocalDateTime timestamp;
    
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ApiResponse(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }
    
    public ApiResponse(boolean success, String message, T data) {
        this(success, message);
        this.data = data;
    }
    
    /**
     * 创建成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, "操作成功", data);
    }
    
    /**
     * 创建成功响应（带消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, message, data);
    }
    
    /**
     * 创建成功响应（无数据）
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(true, message);
    }
    
    /**
     * 创建失败响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(false, message);
    }
    
    /**
     * 创建失败响应（带错误代码）
     */
    public static <T> ApiResponse<T> error(Integer errorCode, String message) {
        ApiResponse<T> response = new ApiResponse<>(false, message);
        response.setErrorCode(errorCode);
        return response;
    }
    
    /**
     * 创建失败响应（带错误详情）
     */
    public static <T> ApiResponse<T> error(String message, String errorDetail) {
        ApiResponse<T> response = new ApiResponse<>(false, message);
        response.setErrorDetail(errorDetail);
        return response;
    }
    
    // Getter 和 Setter 方法
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public T getData() { return data; }
    public void setData(T data) { this.data = data; }
    
    public Integer getErrorCode() { return errorCode; }
    public void setErrorCode(Integer errorCode) { this.errorCode = errorCode; }
    
    public String getErrorDetail() { return errorDetail; }
    public void setErrorDetail(String errorDetail) { this.errorDetail = errorDetail; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", errorCode=" + errorCode +
                ", errorDetail='" + errorDetail + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
