@echo off
REM 知识库测试系统启动脚本 (Windows 版本)
REM 使用方法: scripts\start-app.bat [profile]

setlocal enabledelayedexpansion

REM 默认配置
set DEFAULT_PROFILE=dev
if "%1"=="" (
    set PROFILE=%DEFAULT_PROFILE%
) else (
    set PROFILE=%1
)

echo 🚀 启动知识库测试系统
echo ======================
echo 环境配置: %PROFILE%
echo.

REM 检查 Java 和 Maven
echo 📋 检查环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Java 命令
    pause
    exit /b 1
)

mvn -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Maven 命令
    pause
    exit /b 1
)

java -version
echo.

REM 创建日志目录
echo 📁 创建日志目录...
if not exist "logs\application" mkdir "logs\application"
if not exist "logs\error" mkdir "logs\error"
echo.

REM 清理和编译
echo 🔨 编译项目...
mvn clean compile -q
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译成功
echo.

REM 启动应用
echo 🚀 启动应用...
echo 使用配置文件: %PROFILE%
echo.
echo 📋 访问地址:
echo   应用首页: http://localhost:8080/kb-testing
echo   API 文档: http://localhost:8080/kb-testing/swagger-ui.html
echo   系统健康: http://localhost:8080/kb-testing/api/system/health
echo   系统信息: http://localhost:8080/kb-testing/api/system/info
echo.
echo 📚 API 分组:
echo   全部接口: http://localhost:8080/kb-testing/api-docs/all
echo   系统管理: http://localhost:8080/kb-testing/api-docs/system
echo   Dify 测试: http://localhost:8080/kb-testing/api-docs/dify
echo   测试集管理: http://localhost:8080/kb-testing/api-docs/testset
echo.
echo 按 Ctrl+C 停止应用
echo.

REM 设置 JVM 参数
set JAVA_OPTS=-Xms512m -Xmx1024m -Dspring.profiles.active=%PROFILE%

REM 启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=%PROFILE%

pause
