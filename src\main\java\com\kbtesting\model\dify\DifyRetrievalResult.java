package com.kbtesting.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * Dify 知识库检索结果模型
 * 
 * <AUTHOR> Testing Team
 */
public class DifyRetrievalResult {
    
    @JsonProperty("query")
    private String query;
    
    @JsonProperty("records")
    private List<RetrievalRecord> records;
    
    // 构造函数
    public DifyRetrievalResult() {}
    
    public DifyRetrievalResult(String query, List<RetrievalRecord> records) {
        this.query = query;
        this.records = records;
    }
    
    // Getter 和 Setter 方法
    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }
    
    public List<RetrievalRecord> getRecords() { return records; }
    public void setRecords(List<RetrievalRecord> records) { this.records = records; }
    
    @Override
    public String toString() {
        return "DifyRetrievalResult{" +
                "query='" + query + '\'' +
                ", recordCount=" + (records != null ? records.size() : 0) +
                '}';
    }
    
    /**
     * 检索记录
     */
    public static class RetrievalRecord {
        
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("content")
        private String content;
        
        @JsonProperty("score")
        private Double score;
        
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("metadata")
        private Map<String, Object> metadata;
        
        @JsonProperty("document_id")
        private String documentId;
        
        @JsonProperty("document_name")
        private String documentName;
        
        @JsonProperty("data_source_type")
        private String dataSourceType;
        
        @JsonProperty("segment_id")
        private String segmentId;
        
        @JsonProperty("segment_position")
        private Integer segmentPosition;
        
        @JsonProperty("segment_word_count")
        private Integer segmentWordCount;
        
        @JsonProperty("segment_hit_count")
        private Integer segmentHitCount;
        
        @JsonProperty("segment_index_node_hash")
        private String segmentIndexNodeHash;
        
        // 构造函数
        public RetrievalRecord() {}
        
        public RetrievalRecord(String content, Double score) {
            this.content = content;
            this.score = score;
        }
        
        // Getter 和 Setter 方法
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public Double getScore() { return score; }
        public void setScore(Double score) { this.score = score; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
        
        public String getDocumentId() { return documentId; }
        public void setDocumentId(String documentId) { this.documentId = documentId; }
        
        public String getDocumentName() { return documentName; }
        public void setDocumentName(String documentName) { this.documentName = documentName; }
        
        public String getDataSourceType() { return dataSourceType; }
        public void setDataSourceType(String dataSourceType) { this.dataSourceType = dataSourceType; }
        
        public String getSegmentId() { return segmentId; }
        public void setSegmentId(String segmentId) { this.segmentId = segmentId; }
        
        public Integer getSegmentPosition() { return segmentPosition; }
        public void setSegmentPosition(Integer segmentPosition) { this.segmentPosition = segmentPosition; }
        
        public Integer getSegmentWordCount() { return segmentWordCount; }
        public void setSegmentWordCount(Integer segmentWordCount) { this.segmentWordCount = segmentWordCount; }
        
        public Integer getSegmentHitCount() { return segmentHitCount; }
        public void setSegmentHitCount(Integer segmentHitCount) { this.segmentHitCount = segmentHitCount; }
        
        public String getSegmentIndexNodeHash() { return segmentIndexNodeHash; }
        public void setSegmentIndexNodeHash(String segmentIndexNodeHash) { this.segmentIndexNodeHash = segmentIndexNodeHash; }
        
        @Override
        public String toString() {
            return "RetrievalRecord{" +
                    "id='" + id + '\'' +
                    ", score=" + score +
                    ", documentName='" + documentName + '\'' +
                    ", segmentPosition=" + segmentPosition +
                    '}';
        }
    }
}
