<?xml version="1.0"?>
<!DOCTYPE suppressions PUBLIC
    "-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
    "https://checkstyle.org/dtds/suppressions_1_2.dtd">

<suppressions>
    <!-- 排除测试文件的某些规则 -->
    <suppress checks="MagicNumber" files=".*Test\.java"/>
    <suppress checks="MethodLength" files=".*Test\.java"/>
    <suppress checks="ParameterNumber" files=".*Test\.java"/>
    <suppress checks="DesignForExtension" files=".*Test\.java"/>
    <suppress checks="HideUtilityClassConstructor" files=".*Test\.java"/>
    <suppress checks="FinalParameters" files=".*Test\.java"/>
    
    <!-- 排除配置类的某些规则 -->
    <suppress checks="DesignForExtension" files=".*Config\.java"/>
    <suppress checks="VisibilityModifier" files=".*Config\.java"/>
    
    <!-- 排除模型类的某些规则 -->
    <suppress checks="DesignForExtension" files=".*model.*\.java"/>
    <suppress checks="VisibilityModifier" files=".*model.*\.java"/>
    <suppress checks="HiddenField" files=".*model.*\.java"/>
    
    <!-- 排除示例代码的规则 -->
    <suppress checks=".*" files=".*examples.*\.java"/>
    
    <!-- 排除脚本和批处理文件 -->
    <suppress checks=".*" files=".*\.sh"/>
    <suppress checks=".*" files=".*\.bat"/>
    
    <!-- 排除生成的文件 -->
    <suppress checks=".*" files=".*target.*"/>
    <suppress checks=".*" files=".*\.class"/>
</suppressions>
