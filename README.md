# 知识库自动化测试系统 (Java版)

一套基于Spring Boot的知识库自动化测试解决方案，支持从文档生成测试集、自动化测试知识库、智能评估答案质量，并生成详细的测试报告。

## 🚀 功能特性

- **📝 智能测试集生成**: 从PDF、DOCX、HTML等文档自动生成高质量问答对
- **🧪 自动化测试**: 支持同步/异步模式批量测试知识库
- **🤖 智能答案评估**: 使用DashScope API评估答案的相似度、相关性、完整性和准确性
- **📊 REST API**: 提供完整的REST API接口
- **💻 命令行工具**: 支持命令行模式运行
- **⚙️ 灵活配置**: 支持YAML配置文件，可自定义各种参数
- **🔄 批量处理**: 支持批量生成测试集和批量测试
- **🏗️ 模块化设计**: 基于Spring Boot，易于扩展和维护

## 📁 项目结构

```
knowledge-base-testing/
├── src/main/java/com/kbtesting/
│   ├── KnowledgeBaseTestingApplication.java    # 主应用类
│   ├── config/
│   │   └── AppConfig.java                      # 配置类
│   ├── model/
│   │   ├── TestQuestion.java                   # 测试问题模型
│   │   ├── TestResult.java                     # 测试结果模型
│   │   └── EvaluationScore.java                # 评估分数模型
│   ├── service/
│   │   ├── TestsetGeneratorService.java        # 测试集生成服务
│   │   ├── KnowledgeBaseTesterService.java     # 知识库测试服务
│   │   └── DashScopeService.java               # DashScope API服务
│   ├── controller/
│   │   └── TestController.java                 # REST API控制器
│   ├── cli/
│   │   └── CommandLineRunner.java              # 命令行运行器
│   └── util/
│       ├── DocumentParser.java                 # 文档解析工具
│       └── TextSplitter.java                   # 文本分割工具
├── src/main/resources/
│   └── application.yml                         # 应用配置文件
├── src/test/java/                              # 测试代码
├── data/                                       # 输入文档目录
├── testsets/                                   # 生成的测试集目录
├── results/                                    # 测试结果目录
├── reports/                                    # 测试报告目录
├── temp/                                       # 临时文件目录
├── pom.xml                                     # Maven配置文件
└── README.md                                   # 项目说明文档
```

## 🛠️ 环境要求

- **Java**: JDK 11 或更高版本
- **Maven**: 3.6 或更高版本
- **内存**: 建议 2GB 以上
- **磁盘**: 建议 1GB 以上可用空间

## 📦 安装和构建

### 1. 克隆项目

```bash
git clone <repository-url>
cd knowledge-base-testing
```

### 2. 配置应用

编辑 `src/main/resources/application.yml` 文件，设置您的API密钥和知识库连接信息：

```yaml
api:
  dashscope:
    api-key: "your_dashscope_api_key"
  knowledge-base:
    base-url: "http://your-kb-api.com/api"
    api-key: "your_kb_api_key"
```

### 3. 构建项目

```bash
mvn clean package
```

### 4. 创建必要的目录

```bash
mkdir -p data/documents testsets/generated results/raw reports temp
```

## 🚀 运行方式

### 1. Web服务模式

启动Spring Boot应用：

```bash
java -jar target/knowledge-base-testing-1.0.0.jar
```

应用将在 `http://localhost:8080/kb-testing` 启动。

### 2. 命令行模式

#### 生成测试集

```bash
# 从单个文件生成测试集
java -jar target/knowledge-base-testing-1.0.0.jar generate-testset \
  --input data/documents/document.pdf \
  --output testsets/generated/document_testset.json

# 批量生成测试集
java -jar target/knowledge-base-testing-1.0.0.jar generate-testsets \
  --input-dir data/documents \
  --output-dir testsets/generated
```

#### 运行测试

```bash
# 运行单个测试集
java -jar target/knowledge-base-testing-1.0.0.jar test \
  --testset testsets/generated/document_testset.json

# 批量测试
java -jar target/knowledge-base-testing-1.0.0.jar batch-test \
  --testsets-dir testsets/generated
```

#### 查看帮助

```bash
java -jar target/knowledge-base-testing-1.0.0.jar help
```

## 📡 REST API

### 基础信息

- **Base URL**: `http://localhost:8080/kb-testing/api/test`
- **Content-Type**: `application/json`

### 主要接口

#### 1. 健康检查

```http
GET /api/test/health
```

#### 2. 上传文档生成测试集

```http
POST /api/test/generate-testset
Content-Type: multipart/form-data

file: [文档文件]
outputPath: [输出路径，可选]
```

#### 3. 批量生成测试集

```http
POST /api/test/generate-testsets-batch
Content-Type: application/json

{
  "inputDir": "data/documents",
  "outputDir": "testsets/generated"
}
```

#### 4. 运行测试

```http
POST /api/test/run-test
Content-Type: application/json

{
  "testsetPath": "testsets/generated/document_testset.json",
  "useAsync": true
}
```

#### 5. 获取测试集列表

```http
GET /api/test/testsets?dir=testsets/generated
```

#### 6. 获取测试集详情

```http
GET /api/test/testset/{filename}?dir=testsets/generated
```

## ⚙️ 配置说明

### 主要配置项

```yaml
# API配置
api:
  dashscope:
    api-key: "your_api_key"           # DashScope API密钥
    model: "qwen-max"                 # 使用的模型
    base-url: "https://dashscope.aliyuncs.com/api/v1"
  
  knowledge-base:
    base-url: "http://localhost:3000/api"  # 知识库API地址
    api-key: "your_kb_api_key"             # 知识库API密钥

# 测试集生成配置
testset-generation:
  chunk-size: 1000                   # 文本块大小
  chunk-overlap: 200                 # 文本块重叠
  questions-per-chunk: 3             # 每个文本块生成的问题数

# 知识库测试配置
knowledge-base-testing:
  max-retries: 3                     # 最大重试次数
  batch-size: 10                     # 批处理大小
  parallel-workers: 3                # 并行工作线程数

# 文件路径配置
paths:
  input-documents: "data/documents"   # 输入文档目录
  testsets: "testsets/generated"      # 测试集目录
  test-results: "results/raw"         # 测试结果目录
  reports: "reports"                  # 报告目录
```

## 🧪 测试

运行单元测试：

```bash
mvn test
```

运行集成测试：

```bash
mvn verify
```

## 📊 监控和日志

### 日志配置

日志文件位置：`logs/application/kb-testing.log`

日志级别可在 `application.yml` 中配置：

```yaml
logging:
  level:
    com.kbtesting: INFO
    org.springframework: WARN
```

### 应用监控

Spring Boot Actuator端点：

- 健康检查: `GET /actuator/health`
- 应用信息: `GET /actuator/info`
- 指标信息: `GET /actuator/metrics`

## 🔧 开发指南

### 添加新的文档格式支持

1. 在 `DocumentParser` 类中添加新的解析方法
2. 在 `application.yml` 中添加支持的文件格式
3. 编写相应的单元测试

### 添加新的评估指标

1. 在 `EvaluationScore` 模型中添加新字段
2. 在 `DashScopeService` 中实现评估逻辑
3. 更新相关的API响应

### 自定义知识库接口

1. 实现 `KnowledgeBaseClient` 接口
2. 在配置中指定自定义实现
3. 更新相关的服务类

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `application.yml` 中的API密钥配置
   - 确认API密钥有效且有足够权限

2. **文档解析失败**
   - 确认文档格式受支持
   - 检查文档是否损坏
   - 查看应用日志获取详细错误信息

3. **内存不足**
   - 调整JVM堆内存大小：`-Xmx2g`
   - 减少批处理大小和并行线程数

4. **网络连接问题**
   - 检查知识库API地址是否正确
   - 确认网络连接正常
   - 检查防火墙设置

### 查看日志

```bash
# 查看应用日志
tail -f logs/application/kb-testing.log

# 查看Spring Boot日志
tail -f logs/spring.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

### 开发流程

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮件: <EMAIL>

---

**知识库自动化测试系统** - 让知识库质量评估变得简单高效！ 🎯
