#!/bin/bash

# 知识库测试系统启动脚本
# 使用方法: ./scripts/start-app.sh [profile]

set -e

# 默认配置
DEFAULT_PROFILE="dev"
PROFILE=${1:-$DEFAULT_PROFILE}

echo "🚀 启动知识库测试系统"
echo "======================"
echo "环境配置: $PROFILE"
echo ""

# 检查 Java 和 Maven
echo "📋 检查环境..."
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到 Java 命令"
    exit 1
fi

if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到 Maven 命令"
    exit 1
fi

java -version
echo ""

# 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs/application
mkdir -p logs/error
echo ""

# 清理和编译
echo "🔨 编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"
echo ""

# 启动应用
echo "🚀 启动应用..."
echo "使用配置文件: $PROFILE"
echo ""
echo "📋 访问地址:"
echo "  应用首页: http://localhost:8080/kb-testing"
echo "  API 文档: http://localhost:8080/kb-testing/swagger-ui.html"
echo "  系统健康: http://localhost:8080/kb-testing/api/system/health"
echo "  系统信息: http://localhost:8080/kb-testing/api/system/info"
echo ""
echo "📚 API 分组:"
echo "  全部接口: http://localhost:8080/kb-testing/api-docs/all"
echo "  系统管理: http://localhost:8080/kb-testing/api-docs/system"
echo "  Dify 测试: http://localhost:8080/kb-testing/api-docs/dify"
echo "  测试集管理: http://localhost:8080/kb-testing/api-docs/testset"
echo ""
echo "按 Ctrl+C 停止应用"
echo ""

# 设置 JVM 参数
export JAVA_OPTS="-Xms512m -Xmx1024m -Dspring.profiles.active=$PROFILE"

# 启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=$PROFILE
