package com.kbtesting.cli;

import com.kbtesting.model.TestQuestion;
import com.kbtesting.model.TestResult;
import com.kbtesting.service.TestsetGeneratorService;
import com.kbtesting.service.KnowledgeBaseTesterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 命令行运行器
 * 支持通过命令行参数执行各种操作
 * 
 * <AUTHOR> Testing Team
 */
@Component
public class CommandLineRunner implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(CommandLineRunner.class);
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    @Autowired
    private KnowledgeBaseTesterService knowledgeBaseTesterService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 检查是否有命令行参数
        if (!args.getNonOptionArgs().isEmpty()) {
            String command = args.getNonOptionArgs().get(0);
            
            switch (command.toLowerCase()) {
                case "generate-testset":
                    handleGenerateTestset(args);
                    break;
                case "generate-testsets":
                    handleGenerateTestsets(args);
                    break;
                case "test":
                    handleTest(args);
                    break;
                case "batch-test":
                    handleBatchTest(args);
                    break;
                case "help":
                    printHelp();
                    break;
                default:
                    logger.warn("未知命令: {}", command);
                    printHelp();
            }
            
            // 命令行模式下执行完毕后退出
            System.exit(0);
        }
    }

    /**
     * 处理生成测试集命令
     */
    private void handleGenerateTestset(ApplicationArguments args) {
        String inputFile = getOptionValue(args, "input");
        String outputFile = getOptionValue(args, "output");
        
        if (inputFile == null) {
            logger.error("缺少必需参数: --input");
            return;
        }
        
        logger.info("🔄 开始生成测试集: {}", inputFile);
        
        try {
            List<TestQuestion> testset = testsetGeneratorService.generateFromFile(inputFile, outputFile);
            
            if (!testset.isEmpty()) {
                logger.info("✅ 测试集生成成功!");
                logger.info("   📊 生成问题数: {}", testset.size());
                if (outputFile != null) {
                    logger.info("   📄 输出文件: {}", outputFile);
                }
            } else {
                logger.error("❌ 测试集生成失败");
            }
            
        } catch (Exception e) {
            logger.error("❌ 生成测试集时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 处理批量生成测试集命令
     */
    private void handleGenerateTestsets(ApplicationArguments args) {
        String inputDir = getOptionValue(args, "input-dir", "data/documents");
        String outputDir = getOptionValue(args, "output-dir", "testsets/generated");
        
        logger.info("🔄 开始批量生成测试集");
        logger.info("   📁 输入目录: {}", inputDir);
        logger.info("   📁 输出目录: {}", outputDir);
        
        try {
            List<String> generatedFiles = testsetGeneratorService.processDirectory(inputDir, outputDir);
            
            if (!generatedFiles.isEmpty()) {
                logger.info("✅ 批量生成完成!");
                logger.info("   📊 生成文件数: {}", generatedFiles.size());
                generatedFiles.forEach(file -> logger.info("   📄 {}", file));
            } else {
                logger.warn("⚠️  未生成任何测试集文件");
            }
            
        } catch (Exception e) {
            logger.error("❌ 批量生成测试集时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 处理测试命令
     */
    private void handleTest(ApplicationArguments args) {
        String testsetPath = getOptionValue(args, "testset");
        boolean useAsync = !args.containsOption("sync");
        
        if (testsetPath == null) {
            logger.error("缺少必需参数: --testset");
            return;
        }
        
        logger.info("🧪 开始运行测试: {}", testsetPath);
        logger.info("   ⚙️  执行模式: {}", useAsync ? "异步" : "同步");
        
        try {
            List<TestResult> results = knowledgeBaseTesterService.runTest(testsetPath, useAsync);
            
            if (!results.isEmpty()) {
                // 计算统计信息
                int totalQuestions = results.size();
                long successfulQueries = results.stream()
                    .mapToLong(r -> r.isSuccess() ? 1 : 0)
                    .sum();
                double successRate = (double) successfulQueries / totalQuestions;
                double avgResponseTime = results.stream()
                    .mapToLong(TestResult::getResponseTime)
                    .average()
                    .orElse(0.0);
                
                logger.info("✅ 测试完成!");
                logger.info("   📊 总问题数: {}", totalQuestions);
                logger.info("   📈 成功率: {:.1%}", successRate);
                logger.info("   ⏱️  平均响应时间: {:.0f}ms", avgResponseTime);
                
            } else {
                logger.warn("⚠️  测试结果为空");
            }
            
        } catch (Exception e) {
            logger.error("❌ 运行测试时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 处理批量测试命令
     */
    private void handleBatchTest(ApplicationArguments args) {
        String testsetsDir = getOptionValue(args, "testsets-dir", "testsets/generated");
        boolean useAsync = !args.containsOption("sync");
        
        logger.info("🔄 开始批量测试");
        logger.info("   📁 测试集目录: {}", testsetsDir);
        logger.info("   ⚙️  执行模式: {}", useAsync ? "异步" : "同步");
        
        try {
            // 这里需要实现批量测试逻辑
            // 由于篇幅限制，这里只是示例
            logger.info("✅ 批量测试功能待实现");
            
        } catch (Exception e) {
            logger.error("❌ 批量测试时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 打印帮助信息
     */
    private void printHelp() {
        System.out.println();
        System.out.println("🎯 知识库自动化测试系统 - 命令行工具");
        System.out.println("=" .repeat(60));
        System.out.println();
        System.out.println("📝 可用命令:");
        System.out.println();
        System.out.println("  generate-testset    从单个文件生成测试集");
        System.out.println("    --input <file>      输入文件路径 (必需)");
        System.out.println("    --output <file>     输出文件路径 (可选)");
        System.out.println();
        System.out.println("  generate-testsets   批量生成测试集");
        System.out.println("    --input-dir <dir>   输入目录路径 (默认: data/documents)");
        System.out.println("    --output-dir <dir>  输出目录路径 (默认: testsets/generated)");
        System.out.println();
        System.out.println("  test                运行单个测试集");
        System.out.println("    --testset <file>    测试集文件路径 (必需)");
        System.out.println("    --sync              使用同步模式 (默认: 异步)");
        System.out.println();
        System.out.println("  batch-test          批量运行测试集");
        System.out.println("    --testsets-dir <dir> 测试集目录路径 (默认: testsets/generated)");
        System.out.println("    --sync              使用同步模式 (默认: 异步)");
        System.out.println();
        System.out.println("  help                显示此帮助信息");
        System.out.println();
        System.out.println("💡 使用示例:");
        System.out.println();
        System.out.println("  # 生成测试集");
        System.out.println("  java -jar kb-testing.jar generate-testset --input data/doc.pdf");
        System.out.println();
        System.out.println("  # 批量生成测试集");
        System.out.println("  java -jar kb-testing.jar generate-testsets --input-dir data/");
        System.out.println();
        System.out.println("  # 运行测试");
        System.out.println("  java -jar kb-testing.jar test --testset testsets/doc_testset.json");
        System.out.println();
        System.out.println("  # 批量测试");
        System.out.println("  java -jar kb-testing.jar batch-test --testsets-dir testsets/");
        System.out.println();
        System.out.println("🌐 Web界面: http://localhost:8080/kb-testing");
        System.out.println();
    }

    /**
     * 获取选项值
     */
    private String getOptionValue(ApplicationArguments args, String optionName) {
        List<String> values = args.getOptionValues(optionName);
        return values != null && !values.isEmpty() ? values.get(0) : null;
    }

    /**
     * 获取选项值（带默认值）
     */
    private String getOptionValue(ApplicationArguments args, String optionName, String defaultValue) {
        String value = getOptionValue(args, optionName);
        return value != null ? value : defaultValue;
    }
}
