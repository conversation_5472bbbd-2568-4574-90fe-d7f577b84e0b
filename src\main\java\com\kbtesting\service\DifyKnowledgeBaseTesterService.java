package com.kbtesting.service;

import com.kbtesting.client.DifyKnowledgeBaseClient;
import com.kbtesting.config.AppConfig;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.model.TestResult;
import com.kbtesting.model.dify.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

/**
 * Dify 知识库测试服务
 * 
 * <AUTHOR> Testing Team
 */
@Service
public class DifyKnowledgeBaseTesterService {
    
    private static final Logger logger = LoggerFactory.getLogger(DifyKnowledgeBaseTesterService.class);
    
    @Autowired
    private DifyKnowledgeBaseClient difyClient;
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    @Autowired
    private DashScopeService dashScopeService;
    
    private ExecutorService executorService;
    
    /**
     * 创建测试知识库
     */
    public DifyKnowledgeBase createTestKnowledgeBase(String name, String description) {
        try {
            logger.info("创建测试知识库: {}", name);
            DifyKnowledgeBase knowledgeBase = difyClient.createKnowledgeBase(name, description);
            logger.info("成功创建知识库，ID: {}", knowledgeBase.getId());
            return knowledgeBase;
        } catch (IOException e) {
            logger.error("创建知识库失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建知识库失败", e);
        }
    }
    
    /**
     * 上传测试文档到知识库
     */
    public DifyDocument uploadTestDocument(String datasetId, File documentFile) {
        try {
            logger.info("上传文档到知识库 {}: {}", datasetId, documentFile.getName());
            
            String indexingTechnique = appConfig.getDifyTesting().getKnowledgeBase().getDefaultIndexingTechnique();
            DifyDocument document = difyClient.createDocumentByFile(datasetId, documentFile, indexingTechnique, null);
            
            logger.info("成功上传文档，ID: {}", document.getId());
            
            // 等待文档索引完成
            if (appConfig.getDifyTesting().getTestSettings().isWaitForIndexing()) {
                waitForDocumentIndexing(datasetId, document.getId());
            }
            
            return document;
        } catch (IOException e) {
            logger.error("上传文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传文档失败", e);
        }
    }
    
    /**
     * 通过文本创建测试文档
     */
    public DifyDocument createTestDocumentByText(String datasetId, String name, String text) {
        try {
            logger.info("通过文本创建文档到知识库 {}: {}", datasetId, name);
            
            String indexingTechnique = appConfig.getDifyTesting().getKnowledgeBase().getDefaultIndexingTechnique();
            DifyDocument document = difyClient.createDocumentByText(datasetId, name, text, indexingTechnique, null);
            
            logger.info("成功创建文档，ID: {}", document.getId());
            
            // 等待文档索引完成
            if (appConfig.getDifyTesting().getTestSettings().isWaitForIndexing()) {
                waitForDocumentIndexing(datasetId, document.getId());
            }
            
            return document;
        } catch (IOException e) {
            logger.error("创建文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建文档失败", e);
        }
    }
    
    /**
     * 等待文档索引完成
     */
    private void waitForDocumentIndexing(String datasetId, String documentId) {
        int timeout = appConfig.getDifyTesting().getTestSettings().getIndexingTimeout();
        int checkInterval = 5; // 5秒检查一次
        int maxChecks = timeout / checkInterval;
        
        logger.info("等待文档索引完成，最大等待时间: {} 秒", timeout);
        
        for (int i = 0; i < maxChecks; i++) {
            try {
                Thread.sleep(checkInterval * 1000);
                
                // 检查文档状态
                List<DifyDocument> documents = difyClient.getDocuments(datasetId, 1, 100);
                Optional<DifyDocument> document = documents.stream()
                    .filter(doc -> doc.getId().equals(documentId))
                    .findFirst();
                
                if (document.isPresent()) {
                    String status = document.get().getIndexingStatus();
                    logger.debug("文档索引状态: {}", status);
                    
                    if ("completed".equals(status)) {
                        logger.info("文档索引完成");
                        return;
                    } else if ("error".equals(status)) {
                        logger.error("文档索引失败");
                        throw new RuntimeException("文档索引失败");
                    }
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待文档索引被中断", e);
            } catch (IOException e) {
                logger.warn("检查文档状态失败: {}", e.getMessage());
            }
        }
        
        logger.warn("文档索引超时，继续执行测试");
    }
    
    /**
     * 执行知识库检索测试
     */
    public List<TestResult> runRetrievalTest(String datasetId, List<TestQuestion> testQuestions) {
        logger.info("开始执行知识库检索测试，数据集ID: {}, 测试问题数: {}", datasetId, testQuestions.size());
        
        List<TestResult> results = new ArrayList<>();
        AppConfig.DifyTesting.Retrieval retrievalConfig = appConfig.getDifyTesting().getRetrieval();
        
        for (int i = 0; i < testQuestions.size(); i++) {
            TestQuestion question = testQuestions.get(i);
            logger.info("执行测试 {}/{}: {}", i + 1, testQuestions.size(), 
                       question.getQuestion().substring(0, Math.min(50, question.getQuestion().length())));
            
            TestResult result = testSingleRetrieval(datasetId, question, retrievalConfig);
            results.add(result);
        }
        
        logger.info("检索测试完成，共 {} 个结果", results.size());
        return results;
    }
    
    /**
     * 测试单个检索
     */
    private TestResult testSingleRetrieval(String datasetId, TestQuestion testQuestion, 
                                         AppConfig.DifyTesting.Retrieval retrievalConfig) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行检索
            DifyRetrievalResult retrievalResult = difyClient.retrieveKnowledgeBase(
                datasetId, 
                testQuestion.getQuestion(),
                retrievalConfig.getDefaultTopK(),
                retrievalConfig.getDefaultScoreThreshold()
            );
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            // 构建答案（从检索结果中提取）
            String actualAnswer = buildAnswerFromRetrievalResult(retrievalResult);
            
            // 创建测试结果
            TestResult result = new TestResult(
                testQuestion.getQuestion(),
                testQuestion.getExpectedAnswer(),
                actualAnswer,
                responseTime,
                true
            );
            
            // 添加检索相关的元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("retrievalRecordCount", retrievalResult.getRecords().size());
            metadata.put("topScore", getTopScore(retrievalResult));
            metadata.put("datasetId", datasetId);
            result.setMetadata(metadata);
            
            return result;
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            logger.error("检索测试失败: {}", e.getMessage());
            
            return new TestResult(
                testQuestion.getQuestion(),
                testQuestion.getExpectedAnswer(),
                "",
                responseTime,
                false,
                e.getMessage()
            );
        }
    }
    
    /**
     * 从检索结果构建答案
     */
    private String buildAnswerFromRetrievalResult(DifyRetrievalResult retrievalResult) {
        if (retrievalResult.getRecords() == null || retrievalResult.getRecords().isEmpty()) {
            return "未找到相关内容";
        }
        
        StringBuilder answer = new StringBuilder();
        for (int i = 0; i < Math.min(3, retrievalResult.getRecords().size()); i++) {
            DifyRetrievalResult.RetrievalRecord record = retrievalResult.getRecords().get(i);
            if (i > 0) {
                answer.append("\n\n");
            }
            answer.append(record.getContent());
        }
        
        return answer.toString();
    }
    
    /**
     * 获取最高分数
     */
    private Double getTopScore(DifyRetrievalResult retrievalResult) {
        return retrievalResult.getRecords().stream()
            .mapToDouble(DifyRetrievalResult.RetrievalRecord::getScore)
            .max()
            .orElse(0.0);
    }
    
    /**
     * 运行完整的知识库测试
     */
    public Map<String, Object> runCompleteKnowledgeBaseTest(String testName, List<File> testDocuments, 
                                                          List<TestQuestion> testQuestions) {
        Map<String, Object> testReport = new HashMap<>();
        testReport.put("testName", testName);
        testReport.put("startTime", LocalDateTime.now());
        
        DifyKnowledgeBase knowledgeBase = null;
        List<DifyDocument> uploadedDocuments = new ArrayList<>();
        
        try {
            // 1. 创建测试知识库
            knowledgeBase = createTestKnowledgeBase(testName, "自动化测试知识库 - " + testName);
            testReport.put("knowledgeBaseId", knowledgeBase.getId());
            
            // 2. 上传测试文档
            for (File document : testDocuments) {
                DifyDocument uploadedDoc = uploadTestDocument(knowledgeBase.getId(), document);
                uploadedDocuments.add(uploadedDoc);
            }
            testReport.put("uploadedDocuments", uploadedDocuments.size());
            
            // 3. 执行检索测试
            List<TestResult> testResults = runRetrievalTest(knowledgeBase.getId(), testQuestions);
            testReport.put("testResults", testResults);
            
            // 4. 生成测试统计
            Map<String, Object> statistics = generateTestStatistics(testResults);
            testReport.put("statistics", statistics);
            
            testReport.put("success", true);
            
        } catch (Exception e) {
            logger.error("完整知识库测试失败: {}", e.getMessage(), e);
            testReport.put("success", false);
            testReport.put("error", e.getMessage());
        } finally {
            // 5. 清理资源（如果配置了清理）
            if (appConfig.getDifyTesting().getTestSettings().isCleanupAfterTest() && knowledgeBase != null) {
                cleanupTestKnowledgeBase(knowledgeBase.getId());
            }
            
            testReport.put("endTime", LocalDateTime.now());
        }
        
        return testReport;
    }
    
    /**
     * 生成测试统计信息
     */
    private Map<String, Object> generateTestStatistics(List<TestResult> testResults) {
        Map<String, Object> stats = new HashMap<>();
        
        int totalTests = testResults.size();
        int successfulTests = (int) testResults.stream().filter(TestResult::isSuccess).count();
        int failedTests = totalTests - successfulTests;
        
        double averageResponseTime = testResults.stream()
            .mapToLong(TestResult::getResponseTime)
            .average()
            .orElse(0.0);
        
        stats.put("totalTests", totalTests);
        stats.put("successfulTests", successfulTests);
        stats.put("failedTests", failedTests);
        stats.put("successRate", totalTests > 0 ? (double) successfulTests / totalTests : 0.0);
        stats.put("averageResponseTime", averageResponseTime);
        
        return stats;
    }
    
    /**
     * 清理测试知识库
     */
    private void cleanupTestKnowledgeBase(String datasetId) {
        try {
            logger.info("清理测试知识库: {}", datasetId);
            boolean deleted = difyClient.deleteKnowledgeBase(datasetId);
            if (deleted) {
                logger.info("成功删除测试知识库");
            } else {
                logger.warn("删除测试知识库失败");
            }
        } catch (IOException e) {
            logger.error("清理测试知识库失败: {}", e.getMessage());
        }
    }
}
