# Swagger API 文档使用指南

本指南介绍如何使用知识库测试系统的 Swagger API 文档进行接口测试和开发。

## 🚀 快速访问

### API 文档地址

启动应用后，可以通过以下地址访问 API 文档：

- **Swagger UI**: http://localhost:8080/kb-testing/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/kb-testing/api-docs
- **分组 API 文档**: http://localhost:8080/kb-testing/api-docs/[group-name]

### 分组访问

系统按功能模块对 API 进行了分组：

1. **全部接口**: http://localhost:8080/kb-testing/api-docs/all
2. **系统管理**: http://localhost:8080/kb-testing/api-docs/system
3. **Dify 测试**: http://localhost:8080/kb-testing/api-docs/dify
4. **测试集管理**: http://localhost:8080/kb-testing/api-docs/testset

## 📋 API 分组说明

### 1. 系统管理 (System Management)

**路径**: `/api/system/**`

主要功能：
- 系统健康检查
- 获取系统信息
- 配置管理
- 服务状态监控

**核心接口**:
- `GET /api/system/health` - 系统健康检查
- `GET /api/system/info` - 获取系统信息
- `GET /api/system/config` - 获取配置信息
- `POST /api/system/config/reload` - 重新加载配置

### 2. Dify 测试 (Dify Testing)

**路径**: `/api/dify-test/**`

主要功能：
- Dify 知识库管理
- 文档上传和管理
- 知识库检索测试
- 分段管理

**核心接口**:
- `POST /api/dify-test/knowledge-bases` - 创建知识库
- `GET /api/dify-test/knowledge-bases` - 获取知识库列表
- `POST /api/dify-test/knowledge-bases/{id}/documents/upload` - 上传文档
- `POST /api/dify-test/knowledge-bases/{id}/retrieve` - 检索测试
- `POST /api/dify-test/run-complete-test` - 运行完整测试

### 3. 测试集管理 (Testset Management)

**路径**: `/api/testset/**`

主要功能：
- 从文档生成测试集
- 测试集文件管理
- 测试集验证
- 测试集加载

**核心接口**:
- `POST /api/testset/generate` - 从文件生成测试集
- `POST /api/testset/generate-from-path` - 从路径生成测试集
- `GET /api/testset/load` - 加载测试集
- `POST /api/testset/validate` - 验证测试集格式

## 🔧 使用 Swagger UI

### 1. 接口测试

1. **选择接口分组**: 在页面顶部选择要查看的 API 分组
2. **展开接口**: 点击接口名称展开详细信息
3. **查看参数**: 查看请求参数、响应格式等信息
4. **试用接口**: 点击 "Try it out" 按钮
5. **填写参数**: 根据接口要求填写请求参数
6. **执行请求**: 点击 "Execute" 按钮发送请求
7. **查看响应**: 查看响应状态码、响应体等信息

### 2. 参数说明

#### 请求参数类型

- **Path Parameters**: URL 路径中的参数，如 `{datasetId}`
- **Query Parameters**: URL 查询参数，如 `?page=1&limit=20`
- **Request Body**: 请求体参数，通常为 JSON 格式
- **Form Data**: 表单数据，用于文件上传等

#### 响应格式

所有接口都遵循统一的响应格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "errorCode": null,
  "errorDetail": null,
  "timestamp": "2024-01-01T10:00:00"
}
```

### 3. 认证配置

如果 API 需要认证，可以在 Swagger UI 中配置：

1. 点击页面右上角的 "Authorize" 按钮
2. 输入 API Key 或其他认证信息
3. 点击 "Authorize" 确认
4. 后续请求将自动携带认证信息

## 📝 常用测试场景

### 场景 1: 系统健康检查

```bash
# 1. 检查系统状态
GET /api/system/health

# 2. 获取系统信息
GET /api/system/info

# 3. 查看配置信息
GET /api/system/config
```

### 场景 2: Dify 知识库测试

```bash
# 1. 创建知识库
POST /api/dify-test/knowledge-bases
{
  "name": "测试知识库",
  "description": "这是一个测试知识库"
}

# 2. 上传文档
POST /api/dify-test/knowledge-bases/{datasetId}/documents/upload
# 上传文件: test-document.pdf

# 3. 检索测试
POST /api/dify-test/knowledge-bases/{datasetId}/retrieve
{
  "query": "什么是人工智能？",
  "topK": 5,
  "scoreThreshold": 0.7
}

# 4. 删除知识库
DELETE /api/dify-test/knowledge-bases/{datasetId}
```

### 场景 3: 测试集生成和管理

```bash
# 1. 从文件生成测试集
POST /api/testset/generate
# 上传文件: document.pdf
# 参数: saveToFile=true, outputFileName=testset.json

# 2. 加载测试集
GET /api/testset/load?filePath=testsets/testset.json

# 3. 验证测试集格式
POST /api/testset/validate
# 上传文件: testset.json
```

## 🛠️ 开发集成

### 1. 生成客户端代码

Swagger 支持生成多种语言的客户端代码：

```bash
# 使用 swagger-codegen 生成 Java 客户端
swagger-codegen generate -i http://localhost:8080/kb-testing/api-docs \
  -l java -o ./generated-client

# 生成 Python 客户端
swagger-codegen generate -i http://localhost:8080/kb-testing/api-docs \
  -l python -o ./python-client

# 生成 JavaScript 客户端
swagger-codegen generate -i http://localhost:8080/kb-testing/api-docs \
  -l javascript -o ./js-client
```

### 2. 使用 OpenAPI 规范

可以导出 OpenAPI 规范文件用于其他工具：

```bash
# 下载 OpenAPI JSON
curl http://localhost:8080/kb-testing/api-docs > api-spec.json

# 下载特定分组的规范
curl http://localhost:8080/kb-testing/api-docs/dify > dify-api-spec.json
```

### 3. 集成到 CI/CD

```yaml
# GitHub Actions 示例
name: API Testing
on: [push, pull_request]

jobs:
  api-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Start Application
      run: |
        mvn spring-boot:run &
        sleep 30
    - name: Test APIs
      run: |
        # 使用 newman 测试 API
        newman run postman-collection.json \
          --environment postman-environment.json
    - name: Generate API Docs
      run: |
        curl http://localhost:8080/kb-testing/api-docs > api-docs.json
```

## 📊 API 监控和分析

### 1. 性能监控

可以通过 Swagger UI 观察 API 响应时间：

- 每个请求都会显示执行时间
- 可以比较不同参数下的性能差异
- 识别性能瓶颈接口

### 2. 错误分析

Swagger UI 提供详细的错误信息：

- HTTP 状态码
- 错误响应体
- 请求/响应头信息
- 网络错误详情

### 3. 接口使用统计

可以结合日志分析工具统计：

- 接口调用频率
- 成功/失败率
- 响应时间分布
- 用户使用模式

## 🔍 故障排除

### 常见问题

1. **Swagger UI 无法访问**
   - 检查应用是否正常启动
   - 确认端口和上下文路径配置
   - 查看防火墙设置

2. **接口测试失败**
   - 检查请求参数格式
   - 确认认证信息是否正确
   - 查看服务器日志

3. **文档显示不完整**
   - 检查 Swagger 注解是否正确
   - 确认接口路径映射
   - 重启应用刷新文档

### 调试技巧

1. **使用浏览器开发者工具**
   - 查看网络请求详情
   - 检查 JavaScript 错误
   - 分析响应数据

2. **查看应用日志**
   ```bash
   tail -f logs/application/kb-testing.log
   ```

3. **使用 curl 验证**
   ```bash
   curl -X GET "http://localhost:8080/kb-testing/api/system/health" \
     -H "accept: application/json"
   ```

## 📚 参考资源

- [OpenAPI 3.0 规范](https://swagger.io/specification/)
- [Swagger UI 文档](https://swagger.io/tools/swagger-ui/)
- [SpringDoc OpenAPI 文档](https://springdoc.org/)
- [API 设计最佳实践](https://swagger.io/resources/articles/best-practices-in-api-design/)

---

通过本指南，您可以充分利用 Swagger API 文档进行接口测试、开发集成和问题排查。如有疑问，请参考相关文档或联系开发团队。
