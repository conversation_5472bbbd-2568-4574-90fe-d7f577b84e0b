# 知识库自动化测试系统配置文件

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /kb-testing

# 应用配置
app:
  name: Knowledge Base Testing System
  version: 1.0.0
  description: 自动化知识库测试系统

# API配置
api:
  dashscope:
    api-key: "sk-2374f1a49b0548c8bba5fa63d85d04a5"
    model: "qwen-max"
    embedding-model: "text-embedding-v1"
    base-url: "https://dashscope.aliyuncs.com/api/v1"
    timeout: 30000
  
  knowledge-base:
    base-url: "http://localhost:3000/api"
    api-key: "your_kb_api_key"
    timeout: 30000

# 测试集生成配置
testset-generation:
  chunk-size: 1000
  chunk-overlap: 200
  questions-per-chunk: 3
  supported-formats:
    - ".pdf"
    - ".docx"
    - ".html"
    - ".htm"
    - ".txt"
  
  # 问题生成提示词模板
  question-prompt-template: |
    请根据以下文本内容生成{numQuestions}个高质量的问答对，包含不同难度和类型的问题：
    
    文本内容：
    {textChunk}
    
    请按以下格式返回：
    Q1: [问题1]
    A1: [答案1]
    Q2: [问题2]
    A2: [答案2]
    Q3: [问题3]
    A3: [答案3]

# 知识库测试配置
knowledge-base-testing:
  max-retries: 3
  retry-delay: 1000  # 毫秒
  batch-size: 10
  parallel-workers: 3
  
  # 查询参数
  query-params:
    top-k: 5
    score-threshold: 0.7
    max-tokens: 1000

# 答案评估配置
answer-evaluation:
  similarity-threshold: 0.8
  evaluation-metrics:
    - "similarity"
    - "relevance"
    - "completeness"
    - "accuracy"
  
  # 评估提示词模板
  evaluation-prompt-template: |
    请评估以下答案的质量：
    
    问题：{question}
    标准答案：{expectedAnswer}
    实际答案：{actualAnswer}
    
    请从以下维度评分（1-10分）：
    1. 相似度：实际答案与标准答案的相似程度
    2. 相关性：答案是否回答了问题
    3. 完整性：答案是否完整
    4. 准确性：答案是否准确
    
    请按以下格式返回：
    相似度: [分数]
    相关性: [分数]
    完整性: [分数]
    准确性: [分数]
    总体评分: [分数]
    评价理由: [简要说明]

# 文件路径配置
paths:
  input-documents: "data/documents"
  testsets: "testsets/generated"
  test-results: "results/raw"
  reports: "reports"
  logs: "logs/application"
  temp: "temp"

# 报告配置
reporting:
  formats:
    - "html"
    - "json"
    - "csv"
  include-charts: true
  include-detailed-results: true

# 日志配置
logging:
  level:
    com.kbtesting: INFO
    org.springframework: WARN
    org.apache: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: "logs/application/kb-testing.log"
    max-size: "10MB"
    max-history: 30

# 性能配置
performance:
  max-memory-usage: "2GB"
  cache-enabled: true
  cache-size: 1000
  thread-pool:
    core-size: 5
    max-size: 20
    queue-capacity: 100

# 质量控制配置
quality-control:
  min-question-length: 10
  max-question-length: 200
  min-answer-length: 5
  max-answer-length: 500
  duplicate-threshold: 0.9

# Spring Boot配置
spring:
  application:
    name: knowledge-base-testing
  profiles:
    active: dev
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
