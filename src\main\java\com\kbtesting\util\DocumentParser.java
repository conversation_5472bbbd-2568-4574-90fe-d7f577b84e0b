package com.kbtesting.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 文档解析工具类
 * 支持PDF、DOCX、HTML、TXT等格式
 * 
 * <AUTHOR> Testing Team
 */
@Component
public class DocumentParser {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentParser.class);

    /**
     * 提取文档文本内容
     */
    public String extractText(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("文件不存在: " + filePath);
        }
        
        String fileName = file.getName().toLowerCase();
        
        if (fileName.endsWith(".pdf")) {
            return extractTextFromPdf(filePath);
        } else if (fileName.endsWith(".docx")) {
            return extractTextFromDocx(filePath);
        } else if (fileName.endsWith(".html") || fileName.endsWith(".htm")) {
            return extractTextFromHtml(filePath);
        } else if (fileName.endsWith(".txt")) {
            return extractTextFromTxt(filePath);
        } else {
            throw new IOException("不支持的文件格式: " + fileName);
        }
    }

    /**
     * 从PDF文件提取文本
     */
    private String extractTextFromPdf(String pdfPath) throws IOException {
        logger.info("解析PDF文件: {}", pdfPath);
        
        try (PDDocument document = PDDocument.load(new File(pdfPath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setStartPage(1);
            stripper.setEndPage(document.getNumberOfPages());
            
            String text = stripper.getText(document);
            logger.info("PDF解析完成，提取文本长度: {} 字符", text.length());
            
            return cleanText(text);
            
        } catch (IOException e) {
            logger.error("解析PDF文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 从DOCX文件提取文本
     */
    private String extractTextFromDocx(String docxPath) throws IOException {
        logger.info("解析DOCX文件: {}", docxPath);
        
        try (FileInputStream fis = new FileInputStream(docxPath);
             XWPFDocument document = new XWPFDocument(fis)) {
            
            StringBuilder text = new StringBuilder();
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            
            for (XWPFParagraph paragraph : paragraphs) {
                String paragraphText = paragraph.getText();
                if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                    text.append(paragraphText).append("\n");
                }
            }
            
            String result = text.toString();
            logger.info("DOCX解析完成，提取文本长度: {} 字符", result.length());
            
            return cleanText(result);
            
        } catch (IOException e) {
            logger.error("解析DOCX文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 从HTML文件提取文本
     */
    private String extractTextFromHtml(String htmlPath) throws IOException {
        logger.info("解析HTML文件: {}", htmlPath);
        
        try {
            String html = Files.readString(Paths.get(htmlPath), StandardCharsets.UTF_8);
            Document doc = Jsoup.parse(html);
            
            // 移除script和style标签
            doc.select("script").remove();
            doc.select("style").remove();
            
            String text = doc.body().text();
            logger.info("HTML解析完成，提取文本长度: {} 字符", text.length());
            
            return cleanText(text);
            
        } catch (IOException e) {
            logger.error("解析HTML文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 从TXT文件提取文本
     */
    private String extractTextFromTxt(String txtPath) throws IOException {
        logger.info("读取TXT文件: {}", txtPath);
        
        try {
            String text = Files.readString(Paths.get(txtPath), StandardCharsets.UTF_8);
            logger.info("TXT读取完成，文本长度: {} 字符", text.length());
            
            return cleanText(text);
            
        } catch (IOException e) {
            logger.error("读取TXT文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (text == null) {
            return "";
        }
        
        // 统一换行符
        text = text.replaceAll("\\r\\n", "\n");
        text = text.replaceAll("\\r", "\n");
        
        // 移除多余的空白字符
        text = text.replaceAll("\\n{3,}", "\n\n");
        text = text.replaceAll("[ \\t]{2,}", " ");
        
        // 移除首尾空白
        text = text.trim();
        
        return text;
    }

    /**
     * 检查文件格式是否支持
     */
    public boolean isSupportedFormat(String filePath) {
        String fileName = new File(filePath).getName().toLowerCase();
        return fileName.endsWith(".pdf") || 
               fileName.endsWith(".docx") || 
               fileName.endsWith(".html") || 
               fileName.endsWith(".htm") || 
               fileName.endsWith(".txt");
    }

    /**
     * 获取文件格式
     */
    public String getFileFormat(String filePath) {
        String fileName = new File(filePath).getName().toLowerCase();
        
        if (fileName.endsWith(".pdf")) {
            return "PDF";
        } else if (fileName.endsWith(".docx")) {
            return "DOCX";
        } else if (fileName.endsWith(".html") || fileName.endsWith(".htm")) {
            return "HTML";
        } else if (fileName.endsWith(".txt")) {
            return "TXT";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * 获取文档基本信息
     */
    public DocumentInfo getDocumentInfo(String filePath) throws IOException {
        File file = new File(filePath);
        String format = getFileFormat(filePath);
        long fileSize = file.length();
        
        String text = extractText(filePath);
        int characterCount = text.length();
        int wordCount = text.split("\\s+").length;
        int lineCount = text.split("\n").length;
        
        return new DocumentInfo(
            file.getName(),
            format,
            fileSize,
            characterCount,
            wordCount,
            lineCount
        );
    }

    /**
     * 文档信息类
     */
    public static class DocumentInfo {
        private final String fileName;
        private final String format;
        private final long fileSize;
        private final int characterCount;
        private final int wordCount;
        private final int lineCount;

        public DocumentInfo(String fileName, String format, long fileSize, 
                          int characterCount, int wordCount, int lineCount) {
            this.fileName = fileName;
            this.format = format;
            this.fileSize = fileSize;
            this.characterCount = characterCount;
            this.wordCount = wordCount;
            this.lineCount = lineCount;
        }

        // Getters
        public String getFileName() { return fileName; }
        public String getFormat() { return format; }
        public long getFileSize() { return fileSize; }
        public int getCharacterCount() { return characterCount; }
        public int getWordCount() { return wordCount; }
        public int getLineCount() { return lineCount; }

        @Override
        public String toString() {
            return String.format("DocumentInfo{fileName='%s', format='%s', fileSize=%d, " +
                               "characterCount=%d, wordCount=%d, lineCount=%d}",
                               fileName, format, fileSize, characterCount, wordCount, lineCount);
        }
    }
}
