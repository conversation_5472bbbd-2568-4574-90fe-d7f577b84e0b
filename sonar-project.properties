# SonarQube 项目配置文件

# 项目基本信息
sonar.projectKey=knowledge-base-testing
sonar.projectName=Knowledge Base Testing System
sonar.projectVersion=1.0.0
sonar.projectDescription=自动化知识库测试系统

# 源代码路径
sonar.sources=src/main/java
sonar.tests=src/test/java

# Java 相关配置
sonar.java.source=11
sonar.java.target=11
sonar.java.binaries=target/classes
sonar.java.test.binaries=target/test-classes
sonar.java.libraries=target/dependency/*.jar

# 编码格式
sonar.sourceEncoding=UTF-8

# 排除文件
sonar.exclusions=**/target/**,**/logs/**,**/temp/**,**/*.bat,**/*.sh

# 测试覆盖率
sonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml
sonar.junit.reportPaths=target/surefire-reports

# 代码质量规则
sonar.java.checkstyle.reportPaths=target/checkstyle-result.xml
sonar.java.pmd.reportPaths=target/pmd.xml
sonar.java.spotbugs.reportPaths=target/spotbugsXml.xml

# 重复代码检测
sonar.cpd.java.minimumTokens=100

# 排除测试文件的某些规则
sonar.issue.ignore.multicriteria=e1,e2,e3
sonar.issue.ignore.multicriteria.e1.ruleKey=java:S2699
sonar.issue.ignore.multicriteria.e1.resourceKey=**/src/test/**
sonar.issue.ignore.multicriteria.e2.ruleKey=java:S5786
sonar.issue.ignore.multicriteria.e2.resourceKey=**/src/test/**
sonar.issue.ignore.multicriteria.e3.ruleKey=java:S1192
sonar.issue.ignore.multicriteria.e3.resourceKey=**/src/test/**
