package com.kbtesting.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 测试结果数据模型
 * 
 * <AUTHOR> Testing Team
 */
public class TestResult {
    
    @JsonProperty("question")
    private String question;
    
    @JsonProperty("expected_answer")
    private String expectedAnswer;
    
    @JsonProperty("actual_answer")
    private String actualAnswer;
    
    @JsonProperty("response_time")
    private long responseTime; // 毫秒
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("error_message")
    private String errorMessage;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
    
    @JsonProperty("evaluation")
    private EvaluationScore evaluation;

    // 构造函数
    public TestResult() {
        this.timestamp = LocalDateTime.now();
    }

    public TestResult(String question, String expectedAnswer, String actualAnswer, 
                     long responseTime, boolean success) {
        this();
        this.question = question;
        this.expectedAnswer = expectedAnswer;
        this.actualAnswer = actualAnswer;
        this.responseTime = responseTime;
        this.success = success;
    }

    public TestResult(String question, String expectedAnswer, String actualAnswer, 
                     long responseTime, boolean success, String errorMessage) {
        this(question, expectedAnswer, actualAnswer, responseTime, success);
        this.errorMessage = errorMessage;
    }

    // Getters and Setters
    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getExpectedAnswer() {
        return expectedAnswer;
    }

    public void setExpectedAnswer(String expectedAnswer) {
        this.expectedAnswer = expectedAnswer;
    }

    public String getActualAnswer() {
        return actualAnswer;
    }

    public void setActualAnswer(String actualAnswer) {
        this.actualAnswer = actualAnswer;
    }

    public long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(long responseTime) {
        this.responseTime = responseTime;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public EvaluationScore getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(EvaluationScore evaluation) {
        this.evaluation = evaluation;
    }

    @Override
    public String toString() {
        return "TestResult{" +
                "question='" + question + '\'' +
                ", expectedAnswer='" + expectedAnswer + '\'' +
                ", actualAnswer='" + actualAnswer + '\'' +
                ", responseTime=" + responseTime +
                ", success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                ", timestamp=" + timestamp +
                ", evaluation=" + evaluation +
                '}';
    }
}
