package com.kbtesting.controller;

import com.kbtesting.client.DifyKnowledgeBaseClient;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.model.TestResult;
import com.kbtesting.model.dify.*;
import com.kbtesting.service.DifyKnowledgeBaseTesterService;
import com.kbtesting.service.TestsetGeneratorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * Dify 知识库测试控制器
 *
 * <AUTHOR> Testing Team
 */
@Tag(name = "Dify 测试", description = "Dify 知识库测试相关接口")
@RestController
@RequestMapping("/api/dify-test")
@CrossOrigin(origins = "*")
public class DifyTestController {

    // 常量定义
    private static final String SUCCESS_KEY = "success";
    private static final String ERROR_KEY = "error";
    private static final String MESSAGE_KEY = "message";
    private static final String DATA_KEY = "data";
    private static final String PAGE_KEY = "page";
    private static final String LIMIT_KEY = "limit";
    
    private static final Logger logger = LoggerFactory.getLogger(DifyTestController.class);
    
    @Autowired
    private DifyKnowledgeBaseClient difyClient;
    
    @Autowired
    private DifyKnowledgeBaseTesterService difyTesterService;
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    /**
     * 健康检查
     */
    @Operation(summary = "健康检查", description = "检查 Dify 测试服务的运行状态")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "服务正常",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"status\":\"healthy\",\"service\":\"Dify Knowledge Base Testing\",\"timestamp\":\"2024-01-01T10:00:00Z\"}")))
    })
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "healthy");
        response.put("service", "Dify Knowledge Base Testing");
        response.put("timestamp", new Date());
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建知识库
     */
    @Operation(summary = "创建知识库", description = "在 Dify 中创建新的知识库")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "知识库创建成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/knowledge-bases")
    public ResponseEntity<Map<String, Object>> createKnowledgeBase(
            @Parameter(description = "知识库创建请求", required = true,
                content = @Content(examples = @ExampleObject(value = "{\"name\":\"测试知识库\",\"description\":\"这是一个测试知识库\"}")))
            @RequestBody Map<String, String> request) {
        try {
            String name = request.get("name");
            String description = request.get("description");
            
            if (name == null || name.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(ERROR_KEY, "知识库名称不能为空"));
            }

            DifyKnowledgeBase knowledgeBase = difyTesterService.createTestKnowledgeBase(name, description);

            Map<String, Object> response = new HashMap<>();
            response.put(SUCCESS_KEY, true);
            response.put("knowledgeBase", knowledgeBase);
            response.put(MESSAGE_KEY, "知识库创建成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("创建知识库失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 获取知识库列表
     */
    @GetMapping("/knowledge-bases")
    public ResponseEntity<Map<String, Object>> getKnowledgeBases(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<DifyKnowledgeBase> knowledgeBases = difyClient.getKnowledgeBases(page, limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", knowledgeBases);
            response.put("page", page);
            response.put("limit", limit);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取知识库列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 获取知识库详情
     */
    @GetMapping("/knowledge-bases/{datasetId}")
    public ResponseEntity<Map<String, Object>> getKnowledgeBase(@PathVariable String datasetId) {
        try {
            DifyKnowledgeBase knowledgeBase = difyClient.getKnowledgeBase(datasetId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("knowledgeBase", knowledgeBase);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取知识库详情失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 删除知识库
     */
    @DeleteMapping("/knowledge-bases/{datasetId}")
    public ResponseEntity<Map<String, Object>> deleteKnowledgeBase(@PathVariable String datasetId) {
        try {
            boolean deleted = difyClient.deleteKnowledgeBase(datasetId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", deleted);
            response.put("message", deleted ? "知识库删除成功" : "知识库删除失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除知识库失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 上传文档到知识库
     */
    @PostMapping("/knowledge-bases/{datasetId}/documents/upload")
    public ResponseEntity<Map<String, Object>> uploadDocument(
            @PathVariable String datasetId,
            @RequestParam("file") MultipartFile file) {
        try {
            // 保存上传的文件到临时目录
            Path tempDir = Paths.get("temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }
            
            Path tempFile = tempDir.resolve(file.getOriginalFilename());
            Files.copy(file.getInputStream(), tempFile);
            
            // 上传文档
            DifyDocument document = difyTesterService.uploadTestDocument(datasetId, tempFile.toFile());
            
            // 清理临时文件
            Files.deleteIfExists(tempFile);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("document", document);
            response.put("message", "文档上传成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("上传文档失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 通过文本创建文档
     */
    @PostMapping("/knowledge-bases/{datasetId}/documents/text")
    public ResponseEntity<Map<String, Object>> createDocumentByText(
            @PathVariable String datasetId,
            @RequestBody Map<String, String> request) {
        try {
            String name = request.get("name");
            String text = request.get("text");
            
            if (name == null || name.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "文档名称不能为空"));
            }
            
            if (text == null || text.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "文档内容不能为空"));
            }
            
            DifyDocument document = difyTesterService.createTestDocumentByText(datasetId, name, text);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("document", document);
            response.put("message", "文档创建成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("创建文档失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 获取知识库文档列表
     */
    @GetMapping("/knowledge-bases/{datasetId}/documents")
    public ResponseEntity<Map<String, Object>> getDocuments(
            @PathVariable String datasetId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<DifyDocument> documents = difyClient.getDocuments(datasetId, page, limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", documents);
            response.put("page", page);
            response.put("limit", limit);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取文档列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 删除文档
     */
    @DeleteMapping("/knowledge-bases/{datasetId}/documents/{documentId}")
    public ResponseEntity<Map<String, Object>> deleteDocument(
            @PathVariable String datasetId,
            @PathVariable String documentId) {
        try {
            boolean deleted = difyClient.deleteDocument(datasetId, documentId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", deleted);
            response.put("message", deleted ? "文档删除成功" : "文档删除失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除文档失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 检索知识库
     */
    @PostMapping("/knowledge-bases/{datasetId}/retrieve")
    public ResponseEntity<Map<String, Object>> retrieveKnowledgeBase(
            @PathVariable String datasetId,
            @RequestBody Map<String, Object> request) {
        try {
            String query = (String) request.get("query");
            Integer topK = (Integer) request.get("topK");
            Double scoreThreshold = (Double) request.get("scoreThreshold");
            
            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "查询内容不能为空"));
            }
            
            DifyRetrievalResult result = difyClient.retrieveKnowledgeBase(datasetId, query, topK, scoreThreshold);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("result", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("检索知识库失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 运行检索测试
     */
    @PostMapping("/knowledge-bases/{datasetId}/test-retrieval")
    public ResponseEntity<Map<String, Object>> testRetrieval(
            @PathVariable String datasetId,
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, String>> questionMaps = (List<Map<String, String>>) request.get("questions");
            
            if (questionMaps == null || questionMaps.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "测试问题不能为空"));
            }
            
            // 转换为 TestQuestion 对象
            List<TestQuestion> testQuestions = new ArrayList<>();
            for (Map<String, String> questionMap : questionMaps) {
                String question = questionMap.get("question");
                String expectedAnswer = questionMap.get("expectedAnswer");
                String difficulty = questionMap.get("difficulty");
                if (question != null && !question.trim().isEmpty()) {
                    testQuestions.add(new TestQuestion(question, expectedAnswer != null ? expectedAnswer : "",difficulty));
                }
            }
            
            List<TestResult> testResults = difyTesterService.runRetrievalTest(datasetId, testQuestions);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("testResults", testResults);
            response.put("totalTests", testResults.size());
            response.put("successfulTests", testResults.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("运行检索测试失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 运行完整的知识库测试
     */
    @PostMapping("/run-complete-test")
    public ResponseEntity<Map<String, Object>> runCompleteTest(
            @RequestParam("testName") String testName,
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "testsetPath", required = false) String testsetPath) {
        try {
            if (testName == null || testName.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "测试名称不能为空"));
            }

            if (files == null || files.length == 0) {
                return ResponseEntity.badRequest().body(Map.of("error", "至少需要上传一个测试文档"));
            }

            // 保存上传的文件
            List<File> testDocuments = new ArrayList<>();
            Path tempDir = Paths.get("temp");
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
            }

            for (MultipartFile file : files) {
                Path tempFile = tempDir.resolve(file.getOriginalFilename());
                Files.copy(file.getInputStream(), tempFile);
                testDocuments.add(tempFile.toFile());
            }

            // 加载或生成测试问题
            List<TestQuestion> testQuestions;
            if (testsetPath != null && !testsetPath.trim().isEmpty()) {
                testQuestions = testsetGeneratorService.loadTestset(testsetPath);
            } else {
                // 从上传的文档生成测试集
                testQuestions = new ArrayList<>();
                for (File document : testDocuments) {
                    List<TestQuestion> docQuestions = testsetGeneratorService.generateTestset(document.getAbsolutePath());
                    testQuestions.addAll(docQuestions);
                }
            }

            if (testQuestions.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "没有可用的测试问题"));
            }

            // 运行完整测试
            Map<String, Object> testReport = difyTesterService.runCompleteKnowledgeBaseTest(
                testName, testDocuments, testQuestions);

            // 清理临时文件
            for (File file : testDocuments) {
                try {
                    Files.deleteIfExists(file.toPath());
                } catch (IOException e) {
                    logger.warn("清理临时文件失败: {}", e.getMessage());
                }
            }

            return ResponseEntity.ok(testReport);

        } catch (Exception e) {
            logger.error("运行完整测试失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 获取文档分段
     */
    @GetMapping("/knowledge-bases/{datasetId}/documents/{documentId}/segments")
    public ResponseEntity<Map<String, Object>> getDocumentSegments(
            @PathVariable String datasetId,
            @PathVariable String documentId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<DifySegment> segments = difyClient.getDocumentSegments(datasetId, documentId, page, limit);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", segments);
            response.put("page", page);
            response.put("limit", limit);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取文档分段失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 新增分段
     */
    @PostMapping("/knowledge-bases/{datasetId}/documents/{documentId}/segments")
    public ResponseEntity<Map<String, Object>> createSegment(
            @PathVariable String datasetId,
            @PathVariable String documentId,
            @RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            String keywords = request.get("keywords");

            if (content == null || content.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "分段内容不能为空"));
            }

            DifySegment segment = difyClient.createSegment(datasetId, documentId, content, keywords);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("segment", segment);
            response.put("message", "分段创建成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("创建分段失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 更新分段
     */
    @PutMapping("/knowledge-bases/{datasetId}/documents/{documentId}/segments/{segmentId}")
    public ResponseEntity<Map<String, Object>> updateSegment(
            @PathVariable String datasetId,
            @PathVariable String documentId,
            @PathVariable String segmentId,
            @RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            String keywords = request.get("keywords");

            DifySegment segment = difyClient.updateSegment(datasetId, documentId, segmentId, content, keywords);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("segment", segment);
            response.put("message", "分段更新成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("更新分段失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 删除分段
     */
    @DeleteMapping("/knowledge-bases/{datasetId}/documents/{documentId}/segments/{segmentId}")
    public ResponseEntity<Map<String, Object>> deleteSegment(
            @PathVariable String datasetId,
            @PathVariable String documentId,
            @PathVariable String segmentId) {
        try {
            boolean deleted = difyClient.deleteSegment(datasetId, documentId, segmentId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", deleted);
            response.put("message", deleted ? "分段删除成功" : "分段删除失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("删除分段失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }
}
