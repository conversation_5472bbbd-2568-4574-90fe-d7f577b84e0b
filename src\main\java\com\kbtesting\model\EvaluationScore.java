package com.kbtesting.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 评估分数数据模型
 * 
 * <AUTHOR> Testing Team
 */
public class EvaluationScore {
    
    @JsonProperty("similarity")
    private double similarity;
    
    @JsonProperty("relevance")
    private double relevance;
    
    @JsonProperty("completeness")
    private double completeness;
    
    @JsonProperty("accuracy")
    private double accuracy;
    
    @JsonProperty("overall")
    private double overall;
    
    @JsonProperty("reason")
    private String reason;

    // 构造函数
    public EvaluationScore() {}

    public EvaluationScore(double similarity, double relevance, double completeness, 
                          double accuracy, double overall, String reason) {
        this.similarity = similarity;
        this.relevance = relevance;
        this.completeness = completeness;
        this.accuracy = accuracy;
        this.overall = overall;
        this.reason = reason;
    }

    // Getters and Setters
    public double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(double similarity) {
        this.similarity = similarity;
    }

    public double getRelevance() {
        return relevance;
    }

    public void setRelevance(double relevance) {
        this.relevance = relevance;
    }

    public double getCompleteness() {
        return completeness;
    }

    public void setCompleteness(double completeness) {
        this.completeness = completeness;
    }

    public double getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(double accuracy) {
        this.accuracy = accuracy;
    }

    public double getOverall() {
        return overall;
    }

    public void setOverall(double overall) {
        this.overall = overall;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * 计算总体分数
     */
    public void calculateOverall() {
        this.overall = (similarity + relevance + completeness + accuracy) / 4.0;
    }

    /**
     * 获取分数等级
     */
    public String getScoreLevel() {
        if (overall >= 0.8) {
            return "excellent";
        } else if (overall >= 0.6) {
            return "good";
        } else if (overall >= 0.4) {
            return "fair";
        } else {
            return "poor";
        }
    }

    @Override
    public String toString() {
        return "EvaluationScore{" +
                "similarity=" + similarity +
                ", relevance=" + relevance +
                ", completeness=" + completeness +
                ", accuracy=" + accuracy +
                ", overall=" + overall +
                ", reason='" + reason + '\'' +
                '}';
    }
}
