package com.kbtesting.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbtesting.client.DifyKnowledgeBaseClient;
import com.kbtesting.model.dify.DifyKnowledgeBase;
import com.kbtesting.model.dify.DifyDocument;
import com.kbtesting.model.dify.DifyRetrievalResult;
import com.kbtesting.service.DifyKnowledgeBaseTesterService;
import com.kbtesting.service.TestsetGeneratorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Dify 测试控制器单元测试
 * 
 * <AUTHOR> Testing Team
 */
@WebMvcTest(DifyTestController.class)
class DifyTestControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @MockBean
    private DifyKnowledgeBaseClient difyClient;
    
    @MockBean
    private DifyKnowledgeBaseTesterService difyTesterService;
    
    @MockBean
    private TestsetGeneratorService testsetGeneratorService;
    
    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/dify-test/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("healthy"))
                .andExpect(jsonPath("$.service").value("Dify Knowledge Base Testing"))
                .andExpect(jsonPath("$.timestamp").exists());
    }
    
    @Test
    void testCreateKnowledgeBase() throws Exception {
        // 准备测试数据
        Map<String, String> request = new HashMap<>();
        request.put("name", "测试知识库");
        request.put("description", "这是一个测试知识库");
        
        DifyKnowledgeBase knowledgeBase = new DifyKnowledgeBase();
        knowledgeBase.setId("kb-123");
        knowledgeBase.setName("测试知识库");
        knowledgeBase.setDescription("这是一个测试知识库");
        
        // 模拟服务调用
        when(difyTesterService.createTestKnowledgeBase("测试知识库", "这是一个测试知识库"))
                .thenReturn(knowledgeBase);
        
        // 执行测试
        mockMvc.perform(post("/api/dify-test/knowledge-bases")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.knowledgeBase.id").value("kb-123"))
                .andExpect(jsonPath("$.knowledgeBase.name").value("测试知识库"))
                .andExpect(jsonPath("$.message").value("知识库创建成功"));
        
        // 验证方法调用
        verify(difyTesterService, times(1)).createTestKnowledgeBase("测试知识库", "这是一个测试知识库");
    }
    
    @Test
    void testCreateKnowledgeBaseWithEmptyName() throws Exception {
        // 准备测试数据
        Map<String, String> request = new HashMap<>();
        request.put("name", "");
        request.put("description", "这是一个测试知识库");
        
        // 执行测试
        mockMvc.perform(post("/api/dify-test/knowledge-bases")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("知识库名称不能为空"));
        
        // 验证方法未被调用
        verify(difyTesterService, never()).createTestKnowledgeBase(anyString(), anyString());
    }
    
    @Test
    void testGetKnowledgeBases() throws Exception {
        // 准备测试数据
        DifyKnowledgeBase kb1 = new DifyKnowledgeBase();
        kb1.setId("kb-1");
        kb1.setName("知识库1");
        
        DifyKnowledgeBase kb2 = new DifyKnowledgeBase();
        kb2.setId("kb-2");
        kb2.setName("知识库2");
        
        List<DifyKnowledgeBase> knowledgeBases = Arrays.asList(kb1, kb2);
        
        // 模拟客户端调用
        when(difyClient.getKnowledgeBases(1, 20)).thenReturn(knowledgeBases);
        
        // 执行测试
        mockMvc.perform(get("/api/dify-test/knowledge-bases"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value("kb-1"))
                .andExpect(jsonPath("$.data[1].id").value("kb-2"))
                .andExpect(jsonPath("$.page").value(1))
                .andExpect(jsonPath("$.limit").value(20));
        
        // 验证方法调用
        verify(difyClient, times(1)).getKnowledgeBases(1, 20);
    }
    
    @Test
    void testGetKnowledgeBase() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        DifyKnowledgeBase knowledgeBase = new DifyKnowledgeBase();
        knowledgeBase.setId(datasetId);
        knowledgeBase.setName("测试知识库");
        
        // 模拟客户端调用
        when(difyClient.getKnowledgeBase(datasetId)).thenReturn(knowledgeBase);
        
        // 执行测试
        mockMvc.perform(get("/api/dify-test/knowledge-bases/{datasetId}", datasetId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.knowledgeBase.id").value(datasetId))
                .andExpect(jsonPath("$.knowledgeBase.name").value("测试知识库"));
        
        // 验证方法调用
        verify(difyClient, times(1)).getKnowledgeBase(datasetId);
    }
    
    @Test
    void testDeleteKnowledgeBase() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        
        // 模拟客户端调用
        when(difyClient.deleteKnowledgeBase(datasetId)).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(delete("/api/dify-test/knowledge-bases/{datasetId}", datasetId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("知识库删除成功"));
        
        // 验证方法调用
        verify(difyClient, times(1)).deleteKnowledgeBase(datasetId);
    }
    
    @Test
    void testUploadDocument() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.txt", "text/plain", "测试文档内容".getBytes());
        
        DifyDocument document = new DifyDocument();
        document.setId("doc-456");
        document.setName("test.txt");
        
        // 模拟服务调用
        when(difyTesterService.uploadTestDocument(eq(datasetId), any())).thenReturn(document);
        
        // 执行测试
        mockMvc.perform(multipart("/api/dify-test/knowledge-bases/{datasetId}/documents/upload", datasetId)
                        .file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.document.id").value("doc-456"))
                .andExpect(jsonPath("$.document.name").value("test.txt"))
                .andExpect(jsonPath("$.message").value("文档上传成功"));
        
        // 验证方法调用
        verify(difyTesterService, times(1)).uploadTestDocument(eq(datasetId), any());
    }
    
    @Test
    void testCreateDocumentByText() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        Map<String, String> request = new HashMap<>();
        request.put("name", "测试文档");
        request.put("text", "这是测试文档的内容");
        
        DifyDocument document = new DifyDocument();
        document.setId("doc-456");
        document.setName("测试文档");
        
        // 模拟服务调用
        when(difyTesterService.createTestDocumentByText(datasetId, "测试文档", "这是测试文档的内容"))
                .thenReturn(document);
        
        // 执行测试
        mockMvc.perform(post("/api/dify-test/knowledge-bases/{datasetId}/documents/text", datasetId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.document.id").value("doc-456"))
                .andExpect(jsonPath("$.document.name").value("测试文档"))
                .andExpect(jsonPath("$.message").value("文档创建成功"));
        
        // 验证方法调用
        verify(difyTesterService, times(1)).createTestDocumentByText(datasetId, "测试文档", "这是测试文档的内容");
    }
    
    @Test
    void testCreateDocumentByTextWithEmptyName() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        Map<String, String> request = new HashMap<>();
        request.put("name", "");
        request.put("text", "这是测试文档的内容");
        
        // 执行测试
        mockMvc.perform(post("/api/dify-test/knowledge-bases/{datasetId}/documents/text", datasetId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("文档名称不能为空"));
        
        // 验证方法未被调用
        verify(difyTesterService, never()).createTestDocumentByText(anyString(), anyString(), anyString());
    }
    
    @Test
    void testRetrieveKnowledgeBase() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        Map<String, Object> request = new HashMap<>();
        request.put("query", "什么是人工智能？");
        request.put("topK", 5);
        request.put("scoreThreshold", 0.7);
        
        DifyRetrievalResult.RetrievalRecord record = new DifyRetrievalResult.RetrievalRecord();
        record.setContent("人工智能是计算机科学的一个分支");
        record.setScore(0.9);
        
        DifyRetrievalResult result = new DifyRetrievalResult();
        result.setQuery("什么是人工智能？");
        result.setRecords(Arrays.asList(record));
        
        // 模拟客户端调用
        when(difyClient.retrieveKnowledgeBase(datasetId, "什么是人工智能？", 5, 0.7))
                .thenReturn(result);
        
        // 执行测试
        mockMvc.perform(post("/api/dify-test/knowledge-bases/{datasetId}/retrieve", datasetId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.query").value("什么是人工智能？"))
                .andExpect(jsonPath("$.result.records").isArray())
                .andExpect(jsonPath("$.result.records.length()").value(1));
        
        // 验证方法调用
        verify(difyClient, times(1)).retrieveKnowledgeBase(datasetId, "什么是人工智能？", 5, 0.7);
    }
    
    @Test
    void testRetrieveKnowledgeBaseWithEmptyQuery() throws Exception {
        // 准备测试数据
        String datasetId = "kb-123";
        Map<String, Object> request = new HashMap<>();
        request.put("query", "");
        
        // 执行测试
        mockMvc.perform(post("/api/dify-test/knowledge-bases/{datasetId}/retrieve", datasetId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("查询内容不能为空"));
        
        // 验证方法未被调用
        verify(difyClient, never()).retrieveKnowledgeBase(anyString(), anyString(), anyInt(), anyDouble());
    }
}
