# Sonar 代码质量修复指南

本指南介绍如何使用 SonarQube 进行代码质量分析和修复常见的代码质量问题。

## 🚀 快速开始

### 1. 运行 Sonar 分析

```bash
# Linux/Mac
./scripts/run-sonar-analysis.sh [sonar-server-url] [sonar-token]

# Windows
scripts\run-sonar-analysis.bat [sonar-server-url] [sonar-token]

# 示例
./scripts/run-sonar-analysis.sh http://localhost:9000 your-sonar-token
```

### 2. 查看分析结果

- **Sonar 仪表板**: http://localhost:9000/dashboard?id=knowledge-base-testing
- **本地覆盖率报告**: target/site/jacoco/index.html
- **Checkstyle 报告**: target/site/checkstyle.html
- **PMD 报告**: target/site/pmd.html
- **SpotBugs 报告**: target/site/spotbugs.html

## 📊 已修复的 Sonar 问题

### 1. 空指针检查 (Null Pointer Dereference)

**问题**: 直接调用可能为 null 的对象方法
**修复**: 添加 null 检查

```java
// 修复前
String responseBody = response.body().string();

// 修复后
ResponseBody responseBody = response.body();
if (responseBody != null) {
    String bodyString = responseBody.string();
    // 处理逻辑
} else {
    throw new IOException("响应体为空");
}
```

### 2. 资源管理 (Resource Leak)

**问题**: 资源未正确关闭
**修复**: 使用 try-with-resources 语句

```java
// 修复前
Response response = httpClient.newCall(request).execute();
// 可能忘记关闭 response

// 修复后
try (Response response = httpClient.newCall(request).execute()) {
    // 处理逻辑
} // 自动关闭资源
```

### 3. 字符串比较 (String Comparison)

**问题**: 使用 `toLowerCase()` 时未指定 Locale
**修复**: 使用 `Locale.ROOT`

```java
// 修复前
text.toLowerCase()

// 修复后
text.toLowerCase(Locale.ROOT)
```

### 4. 异常处理 (Exception Handling)

**问题**: 空的 catch 块或不当的异常处理
**修复**: 添加适当的异常处理逻辑

```java
// 修复前
try {
    Thread.sleep(retryDelay);
} catch (InterruptedException ie) {
    // 空的 catch 块
}

// 修复后
try {
    Thread.sleep(retryDelay);
} catch (InterruptedException ie) {
    Thread.currentThread().interrupt();
    logger.warn("线程被中断");
}
```

### 5. 参数验证 (Parameter Validation)

**问题**: 缺少输入参数验证
**修复**: 添加参数验证

```java
// 修复前
private boolean validateQuestion(TestQuestion question, String originalText) {
    // 直接使用参数
}

// 修复后
private boolean validateQuestion(TestQuestion question, String originalText) {
    if (question == null || question.getQuestion() == null || question.getExpectedAnswer() == null) {
        return false;
    }
    // 其他验证逻辑
}
```

## 🔧 常见 Sonar 规则修复

### 1. 代码复杂度问题

**规则**: `java:S3776` - 认知复杂度过高
**修复方法**:
- 将复杂方法拆分为多个小方法
- 使用早期返回减少嵌套
- 提取重复逻辑到私有方法

### 2. 代码重复问题

**规则**: `java:S1192` - 字符串字面量重复
**修复方法**:
- 定义常量替代重复的字符串
- 使用配置文件存储可变字符串

```java
// 修复前
logger.error("创建知识库失败: " + e.getMessage());
logger.error("创建知识库失败: 网络连接超时");

// 修复后
private static final String CREATE_KB_ERROR_PREFIX = "创建知识库失败: ";
logger.error(CREATE_KB_ERROR_PREFIX + e.getMessage());
logger.error(CREATE_KB_ERROR_PREFIX + "网络连接超时");
```

### 3. 安全问题

**规则**: `java:S2068` - 硬编码密码
**修复方法**:
- 使用配置文件或环境变量
- 使用加密存储敏感信息

### 4. 性能问题

**规则**: `java:S1149` - 避免使用同步集合
**修复方法**:
- 使用并发集合类
- 考虑使用不可变集合

### 5. 可维护性问题

**规则**: `java:S1118` - 工具类应该有私有构造函数
**修复方法**:

```java
public final class UtilityClass {
    private UtilityClass() {
        throw new UnsupportedOperationException("工具类不应被实例化");
    }
    
    public static void utilityMethod() {
        // 工具方法
    }
}
```

## 📋 质量门配置

### 默认质量门条件

- **覆盖率**: > 80%
- **重复代码**: < 3%
- **可维护性评级**: A
- **可靠性评级**: A
- **安全评级**: A
- **新代码覆盖率**: > 80%
- **新代码重复**: < 3%

### 自定义质量门

可以在 SonarQube 管理界面中创建自定义质量门：

1. 登录 SonarQube 管理界面
2. 进入 Quality Gates 页面
3. 创建新的质量门或修改现有质量门
4. 设置适合项目的条件

## 🛠️ 持续集成配置

### Jenkins 集成

```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                sh 'mvn clean compile'
            }
        }
        stage('Test') {
            steps {
                sh 'mvn test'
            }
        }
        stage('SonarQube Analysis') {
            steps {
                withSonarQubeEnv('SonarQube') {
                    sh 'mvn sonar:sonar'
                }
            }
        }
        stage('Quality Gate') {
            steps {
                timeout(time: 1, unit: 'HOURS') {
                    waitForQualityGate abortPipeline: true
                }
            }
        }
    }
}
```

### GitHub Actions 集成

```yaml
name: SonarQube Analysis
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  sonarqube:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    - name: Cache SonarQube packages
      uses: actions/cache@v3
      with:
        path: ~/.sonar/cache
        key: ${{ runner.os }}-sonar
    - name: Run SonarQube Analysis
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: mvn sonar:sonar
```

## 📈 代码质量最佳实践

### 1. 编写高质量代码

- **单一职责原则**: 每个类和方法只负责一个功能
- **开闭原则**: 对扩展开放，对修改关闭
- **依赖倒置**: 依赖抽象而不是具体实现
- **接口隔离**: 使用小而专一的接口

### 2. 测试覆盖率

- 目标覆盖率: 80% 以上
- 重点测试核心业务逻辑
- 编写有意义的测试用例
- 避免为了覆盖率而写无意义的测试

### 3. 代码审查

- 使用 Pull Request 进行代码审查
- 关注代码质量而不仅仅是功能
- 检查 Sonar 报告中的问题
- 确保新代码符合质量标准

### 4. 持续改进

- 定期查看 Sonar 报告
- 设置质量门阻止低质量代码合并
- 重构遗留代码
- 培训团队成员代码质量意识

## 🔍 故障排除

### 常见问题

1. **Sonar 服务器连接失败**
   - 检查服务器地址和端口
   - 验证网络连接
   - 确认 Sonar 服务正在运行

2. **认证失败**
   - 检查 Token 是否正确
   - 验证用户权限
   - 确认项目权限设置

3. **分析失败**
   - 检查项目编译是否成功
   - 查看详细错误日志
   - 验证 sonar-project.properties 配置

4. **覆盖率报告为空**
   - 确认测试已执行
   - 检查 JaCoCo 插件配置
   - 验证测试路径设置

### 日志查看

```bash
# 查看 Maven 详细日志
mvn sonar:sonar -X

# 查看 Sonar 服务器日志
tail -f $SONAR_HOME/logs/sonar.log
```

## 📚 参考资源

- [SonarQube 官方文档](https://docs.sonarqube.org/)
- [SonarQube Java 规则](https://rules.sonarsource.com/java)
- [Maven SonarQube 插件](https://docs.sonarqube.org/latest/analysis/scan/sonarscanner-for-maven/)
- [JaCoCo 覆盖率工具](https://www.jacoco.org/jacoco/trunk/doc/)

---

通过遵循本指南，您可以有效地使用 SonarQube 提高代码质量，减少技术债务，并建立可持续的代码质量管理流程。
