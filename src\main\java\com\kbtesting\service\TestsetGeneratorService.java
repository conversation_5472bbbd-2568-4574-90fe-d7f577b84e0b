package com.kbtesting.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbtesting.config.AppConfig;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.util.DocumentParser;
import com.kbtesting.util.TextSplitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试集生成服务
 * 
 * <AUTHOR> Testing Team
 */
@Service
public class TestsetGeneratorService {
    
    private static final Logger logger = LoggerFactory.getLogger(TestsetGeneratorService.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private DashScopeService dashScopeService;
    
    @Autowired
    private DocumentParser documentParser;
    
    @Autowired
    private TextSplitter textSplitter;
    
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 从文件生成测试集
     */
    public List<TestQuestion> generateFromFile(String filePath, String outputPath) {
        logger.info("开始处理文件: {}", filePath);
        
        try {
            // 1. 提取文本内容
            String rawText = documentParser.extractText(filePath);
            logger.info("已提取文本，长度: {} 字符", rawText.length());
            
            // 2. 分割文本
            List<String> chunks = textSplitter.splitText(rawText, 
                appConfig.getTestsetGeneration().getChunkSize(),
                appConfig.getTestsetGeneration().getChunkOverlap());
            logger.info("已分块，共 {} 个文本块", chunks.size());
            
            // 3. 生成问答对
            List<TestQuestion> allQuestions = new ArrayList<>();
            int questionsPerChunk = appConfig.getTestsetGeneration().getQuestionsPerChunk();
            
            for (int i = 0; i < chunks.size(); i++) {
                logger.info("正在处理第 {}/{} 个文本块...", i + 1, chunks.size());
                
                try {
                    List<TestQuestion> chunkQuestions = generateQuestionsFromChunk(chunks.get(i), questionsPerChunk);
                    
                    // 验证问题质量
                    List<TestQuestion> validQuestions = chunkQuestions.stream()
                        .filter(q -> validateQuestion(q, rawText))
                        .collect(Collectors.toList());
                    
                    allQuestions.addAll(validQuestions);
                    
                } catch (Exception e) {
                    logger.error("处理第 {} 个文本块时出错: {}", i + 1, e.getMessage());
                }
            }
            
            // 4. 后处理：去重
            logger.info("正在进行后处理...");
            List<TestQuestion> deduplicatedQuestions = deduplicateQuestions(allQuestions);
            
            // 5. 保存结果
            if (outputPath != null) {
                saveTestset(deduplicatedQuestions, outputPath);
                logger.info("测试集已保存至: {}", outputPath);
            }
            
            logger.info("测试集生成完成，共生成 {} 个问题", deduplicatedQuestions.size());
            return deduplicatedQuestions;
            
        } catch (Exception e) {
            logger.error("生成测试集失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量处理目录中的文件
     */
    public List<String> processDirectory(String inputDir, String outputDir) {
        logger.info("开始批量处理目录: {}", inputDir);
        
        List<String> generatedFiles = new ArrayList<>();
        List<String> supportedFormats = appConfig.getTestsetGeneration().getSupportedFormats();
        
        try {
            Files.walk(Paths.get(inputDir))
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return supportedFormats.stream().anyMatch(fileName::endsWith);
                })
                .forEach(path -> {
                    try {
                        String fileName = path.getFileName().toString();
                        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                        String outputPath = Paths.get(outputDir, baseName + "_testset.json").toString();
                        
                        generateFromFile(path.toString(), outputPath);
                        generatedFiles.add(outputPath);
                        
                    } catch (Exception e) {
                        logger.error("处理文件失败 {}: {}", path, e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            logger.error("遍历目录失败: {}", e.getMessage());
        }
        
        logger.info("批量处理完成，共生成 {} 个测试集", generatedFiles.size());
        return generatedFiles;
    }

    /**
     * 从文本块生成问答对
     */
    private List<TestQuestion> generateQuestionsFromChunk(String textChunk, int numQuestions) {
        String prompt = appConfig.getTestsetGeneration().getQuestionPromptTemplate()
            .replace("{numQuestions}", String.valueOf(numQuestions))
            .replace("{textChunk}", textChunk);
        
        try {
            String response = dashScopeService.generateText(prompt);
            return parseQuestionsFromResponse(response);
            
        } catch (Exception e) {
            logger.error("生成问答对失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 解析LLM响应中的问答对
     */
    private List<TestQuestion> parseQuestionsFromResponse(String response) {
        List<TestQuestion> questions = new ArrayList<>();
        String[] lines = response.split("\n");
        
        String currentQuestion = null;
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            
            if (line.matches("^Q\\d+:.*")) {
                // 问题行
                currentQuestion = line.replaceFirst("^Q\\d+:\\s*", "");
            } else if (line.matches("^A\\d+:.*") && currentQuestion != null) {
                // 答案行
                String answer = line.replaceFirst("^A\\d+:\\s*", "");
                String difficulty = estimateDifficulty(currentQuestion, answer);
                
                questions.add(new TestQuestion(currentQuestion, answer, difficulty));
                currentQuestion = null;
            }
        }
        
        return questions;
    }

    /**
     * 估计问题难度
     */
    private String estimateDifficulty(String question, String answer) {
        // 简单的启发式难度评估
        int questionLength = question.length();
        int answerLength = answer.length();
        
        if (questionLength > 100 || answerLength > 200) {
            return "hard";
        } else if (questionLength > 50 || answerLength > 100) {
            return "medium";
        } else {
            return "easy";
        }
    }

    /**
     * 验证问题质量
     */
    private boolean validateQuestion(TestQuestion question, String originalText) {
        // 基本长度检查
        int minQuestionLength = appConfig.getQualityControl().getMinQuestionLength();
        int maxQuestionLength = appConfig.getQualityControl().getMaxQuestionLength();
        int minAnswerLength = appConfig.getQualityControl().getMinAnswerLength();
        int maxAnswerLength = appConfig.getQualityControl().getMaxAnswerLength();
        
        if (question.getQuestion().length() < minQuestionLength || 
            question.getQuestion().length() > maxQuestionLength ||
            question.getExpectedAnswer().length() < minAnswerLength ||
            question.getExpectedAnswer().length() > maxAnswerLength) {
            return false;
        }
        
        // 可以添加更多验证逻辑，如使用LLM验证答案是否可以从原文中得出
        return true;
    }

    /**
     * 去重测试集
     */
    private List<TestQuestion> deduplicateQuestions(List<TestQuestion> questions) {
        Set<String> seen = new HashSet<>();
        List<TestQuestion> deduplicated = new ArrayList<>();
        
        for (TestQuestion question : questions) {
            String key = question.getQuestion().toLowerCase() + "|" + 
                        question.getExpectedAnswer().toLowerCase();
            
            if (!seen.contains(key)) {
                seen.add(key);
                deduplicated.add(question);
            }
        }
        
        return deduplicated;
    }

    /**
     * 保存测试集到文件
     */
    private void saveTestset(List<TestQuestion> questions, String outputPath) throws IOException {
        // 确保输出目录存在
        Path outputDir = Paths.get(outputPath).getParent();
        if (outputDir != null && !Files.exists(outputDir)) {
            Files.createDirectories(outputDir);
        }
        
        objectMapper.writerWithDefaultPrettyPrinter()
            .writeValue(new File(outputPath), questions);
    }

    /**
     * 加载测试集文件
     */
    public List<TestQuestion> loadTestset(String testsetPath) throws IOException {
        return objectMapper.readValue(new File(testsetPath), new TypeReference<List<TestQuestion>>() {});
    }

	public List<TestQuestion> generateTestset(String absolutePath) {
		return null;
	}
}
