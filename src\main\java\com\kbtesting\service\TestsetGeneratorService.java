package com.kbtesting.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbtesting.config.AppConfig;
import com.kbtesting.model.TestQuestion;
import com.kbtesting.util.DocumentParser;
import com.kbtesting.util.TextSplitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试集生成服务
 * 
 * <AUTHOR> Testing Team
 */
@Service
public class TestsetGeneratorService {
    
    private static final Logger logger = LoggerFactory.getLogger(TestsetGeneratorService.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private DashScopeService dashScopeService;
    
    @Autowired
    private DocumentParser documentParser;
    
    @Autowired
    private TextSplitter textSplitter;
    
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 从文件生成测试集
     */
    public List<TestQuestion> generateFromFile(String filePath, String outputPath) {
        logger.info("开始处理文件: {}", filePath);
        
        try {
            // 1. 提取文本内容
            String rawText = documentParser.extractText(filePath);
            logger.info("已提取文本，长度: {} 字符", rawText.length());
            
            // 2. 分割文本
            List<String> chunks = textSplitter.splitText(rawText, 
                appConfig.getTestsetGeneration().getChunkSize(),
                appConfig.getTestsetGeneration().getChunkOverlap());
            logger.info("已分块，共 {} 个文本块", chunks.size());
            
            // 3. 生成问答对
            List<TestQuestion> allQuestions = new ArrayList<>();
            int questionsPerChunk = appConfig.getTestsetGeneration().getQuestionsPerChunk();
            
            for (int i = 0; i < chunks.size(); i++) {
                logger.info("正在处理第 {}/{} 个文本块...", i + 1, chunks.size());
                
                try {
                    List<TestQuestion> chunkQuestions = generateQuestionsFromChunk(chunks.get(i), questionsPerChunk);
                    
                    // 验证问题质量
                    List<TestQuestion> validQuestions = chunkQuestions.stream()
                        .filter(q -> validateQuestion(q, rawText))
                        .collect(Collectors.toList());
                    
                    allQuestions.addAll(validQuestions);
                    
                } catch (Exception e) {
                    logger.error("处理第 {} 个文本块时出错: {}", i + 1, e.getMessage());
                }
            }
            
            // 4. 后处理：去重
            logger.info("正在进行后处理...");
            List<TestQuestion> deduplicatedQuestions = deduplicateQuestions(allQuestions);
            
            // 5. 保存结果
            if (outputPath != null) {
                saveTestset(deduplicatedQuestions, outputPath);
                logger.info("测试集已保存至: {}", outputPath);
            }
            
            logger.info("测试集生成完成，共生成 {} 个问题", deduplicatedQuestions.size());
            return deduplicatedQuestions;
            
        } catch (Exception e) {
            logger.error("生成测试集失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量处理目录中的文件
     */
    public List<String> processDirectory(String inputDir, String outputDir) {
        logger.info("开始批量处理目录: {}", inputDir);
        
        List<String> generatedFiles = new ArrayList<>();
        List<String> supportedFormats = appConfig.getTestsetGeneration().getSupportedFormats();
        
        try {
            Files.walk(Paths.get(inputDir))
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return supportedFormats.stream().anyMatch(fileName::endsWith);
                })
                .forEach(path -> {
                    try {
                        String fileName = path.getFileName().toString();
                        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                        String outputPath = Paths.get(outputDir, baseName + "_testset.json").toString();
                        
                        generateFromFile(path.toString(), outputPath);
                        generatedFiles.add(outputPath);
                        
                    } catch (Exception e) {
                        logger.error("处理文件失败 {}: {}", path, e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            logger.error("遍历目录失败: {}", e.getMessage());
        }
        
        logger.info("批量处理完成，共生成 {} 个测试集", generatedFiles.size());
        return generatedFiles;
    }

    /**
     * 从文本块生成问答对
     */
    private List<TestQuestion> generateQuestionsFromChunk(String textChunk, int numQuestions) {
        String prompt = appConfig.getTestsetGeneration().getQuestionPromptTemplate()
            .replace("{numQuestions}", String.valueOf(numQuestions))
            .replace("{textChunk}", textChunk);
        
        try {
            String response = dashScopeService.generateText(prompt);
            return parseQuestionsFromResponse(response);
            
        } catch (Exception e) {
            logger.error("生成问答对失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 解析LLM响应中的问答对
     */
    private List<TestQuestion> parseQuestionsFromResponse(String response) {
        List<TestQuestion> questions = new ArrayList<>();
        String[] lines = response.split("\n");
        
        String currentQuestion = null;
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            
            if (line.matches("^Q\\d+:.*")) {
                // 问题行
                currentQuestion = line.replaceFirst("^Q\\d+:\\s*", "");
            } else if (line.matches("^A\\d+:.*") && currentQuestion != null) {
                // 答案行
                String answer = line.replaceFirst("^A\\d+:\\s*", "");
                String difficulty = estimateDifficulty(currentQuestion, answer);
                
                questions.add(new TestQuestion(currentQuestion, answer, difficulty));
                currentQuestion = null;
            }
        }
        
        return questions;
    }

    /**
     * 估计问题难度
     */
    private String estimateDifficulty(String question, String answer) {
        // 简单的启发式难度评估
        int questionLength = question.length();
        int answerLength = answer.length();
        
        if (questionLength > 100 || answerLength > 200) {
            return "hard";
        } else if (questionLength > 50 || answerLength > 100) {
            return "medium";
        } else {
            return "easy";
        }
    }

    /**
     * 验证问题质量
     */
    private boolean validateQuestion(TestQuestion question, String originalText) {
        if (question == null || question.getQuestion() == null || question.getExpectedAnswer() == null) {
            return false;
        }

        // 基本长度检查
        AppConfig.QualityControl qualityControl = appConfig.getQualityControl();
        int minQuestionLength = qualityControl.getMinQuestionLength();
        int maxQuestionLength = qualityControl.getMaxQuestionLength();
        int minAnswerLength = qualityControl.getMinAnswerLength();
        int maxAnswerLength = qualityControl.getMaxAnswerLength();

        int questionLength = question.getQuestion().length();
        int answerLength = question.getExpectedAnswer().length();

        if (questionLength < minQuestionLength ||
            questionLength > maxQuestionLength ||
            answerLength < minAnswerLength ||
            answerLength > maxAnswerLength) {
            return false;
        }

        // 检查问题和答案是否为空或只包含空白字符
        if (question.getQuestion().trim().isEmpty() || question.getExpectedAnswer().trim().isEmpty()) {
            return false;
        }

        // 可以添加更多验证逻辑，如使用LLM验证答案是否可以从原文中得出
        return true;
    }

    /**
     * 去重测试集
     */
    private List<TestQuestion> deduplicateQuestions(List<TestQuestion> questions) {
        if (questions == null || questions.isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> seen = new HashSet<>();
        List<TestQuestion> deduplicated = new ArrayList<>();

        for (TestQuestion question : questions) {
            if (question == null || question.getQuestion() == null || question.getExpectedAnswer() == null) {
                continue; // 跳过无效的问题
            }

            String key = question.getQuestion().toLowerCase(Locale.ROOT) + "|" +
                        question.getExpectedAnswer().toLowerCase(Locale.ROOT);

            if (!seen.contains(key)) {
                seen.add(key);
                deduplicated.add(question);
            }
        }

        return deduplicated;
    }

    /**
     * 保存测试集到文件
     */
    private void saveTestset(List<TestQuestion> questions, String outputPath) throws IOException {
        // 确保输出目录存在
        Path outputDir = Paths.get(outputPath).getParent();
        if (outputDir != null && !Files.exists(outputDir)) {
            Files.createDirectories(outputDir);
        }
        
        objectMapper.writerWithDefaultPrettyPrinter()
            .writeValue(new File(outputPath), questions);
    }

    /**
     * 加载测试集文件
     */
    public List<TestQuestion> loadTestset(String testsetPath) throws IOException {
        return objectMapper.readValue(new File(testsetPath), new TypeReference<List<TestQuestion>>() {});
    }

    /**
     * 从文件路径生成测试集（不保存到文件）
     *
     * @param filePath 文件的绝对路径
     * @return 生成的测试问题列表
     */
    public List<TestQuestion> generateTestset(String filePath) {
        logger.info("开始从文件生成测试集: {}", filePath);

        try {
            // 验证文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                logger.error("文件不存在: {}", filePath);
                return Collections.emptyList();
            }

            // 检查文件格式是否支持
            String fileName = file.getName().toLowerCase();
            List<String> supportedFormats = appConfig.getTestsetGeneration().getSupportedFormats();
            boolean isSupported = supportedFormats.stream()
                .anyMatch(format -> fileName.endsWith(format.toLowerCase()));

            if (!isSupported) {
                logger.error("不支持的文件格式: {}，支持的格式: {}", fileName, supportedFormats);
                return Collections.emptyList();
            }

            // 1. 提取文本内容
            String rawText = documentParser.extractText(filePath);
            if (rawText == null || rawText.trim().isEmpty()) {
                logger.error("无法从文件中提取文本内容: {}", filePath);
                return Collections.emptyList();
            }

            logger.info("已提取文本，长度: {} 字符", rawText.length());

            // 2. 分割文本
            AppConfig.TestsetGeneration config = appConfig.getTestsetGeneration();
            List<String> chunks = textSplitter.splitText(rawText,
                config.getChunkSize(),
                config.getChunkOverlap());
            logger.info("已分块，共 {} 个文本块", chunks.size());

            if (chunks.isEmpty()) {
                logger.warn("文本分块后为空: {}", filePath);
                return Collections.emptyList();
            }

            // 3. 生成问答对
            List<TestQuestion> allQuestions = new ArrayList<>();
            int questionsPerChunk = config.getQuestionsPerChunk();
            int totalChunks = chunks.size();

            for (int i = 0; i < totalChunks; i++) {
                String chunk = chunks.get(i);
                logger.info("正在处理第 {}/{} 个文本块（长度: {} 字符）...",
                           i + 1, totalChunks, chunk.length());

                try {
                    // 跳过过短的文本块
                    if (chunk.trim().length() < 50) {
                        logger.debug("跳过过短的文本块: {} 字符", chunk.length());
                        continue;
                    }

                    List<TestQuestion> chunkQuestions = generateQuestionsFromChunk(chunk, questionsPerChunk);

                    if (!chunkQuestions.isEmpty()) {
                        // 验证问题质量
                        List<TestQuestion> validQuestions = chunkQuestions.stream()
                            .filter(q -> validateQuestion(q, rawText))
                            .collect(Collectors.toList());

                        allQuestions.addAll(validQuestions);
                        logger.debug("从文本块 {} 生成了 {} 个有效问题", i + 1, validQuestions.size());
                    } else {
                        logger.debug("文本块 {} 未生成任何问题", i + 1);
                    }

                } catch (Exception e) {
                    logger.error("处理第 {} 个文本块时出错: {}", i + 1, e.getMessage());
                    // 继续处理下一个文本块，不中断整个流程
                }
            }

            // 4. 后处理：去重和排序
            logger.info("正在进行后处理（去重、排序）...");
            List<TestQuestion> deduplicatedQuestions = deduplicateQuestions(allQuestions);

            // 按难度排序（可选）
            deduplicatedQuestions.sort((q1, q2) -> {
                String diff1 = q1.getDifficulty() != null ? q1.getDifficulty() : "medium";
                String diff2 = q2.getDifficulty() != null ? q2.getDifficulty() : "medium";
                return getDifficultyOrder(diff1) - getDifficultyOrder(diff2);
            });

            logger.info("测试集生成完成，共生成 {} 个问题（原始: {} 个）",
                       deduplicatedQuestions.size(), allQuestions.size());

            // 记录统计信息
            logGenerationStatistics(deduplicatedQuestions, filePath);

            return deduplicatedQuestions;

        } catch (Exception e) {
            logger.error("生成测试集失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取难度排序值
     */
    private int getDifficultyOrder(String difficulty) {
        switch (difficulty.toLowerCase()) {
            case "easy": return 1;
            case "medium": return 2;
            case "hard": return 3;
            default: return 2; // 默认为中等难度
        }
    }

    /**
     * 记录生成统计信息
     */
    private void logGenerationStatistics(List<TestQuestion> questions, String filePath) {
        if (questions.isEmpty()) {
            logger.warn("未生成任何测试问题");
            return;
        }

        Map<String, Long> difficultyCount = questions.stream()
            .collect(Collectors.groupingBy(
                q -> q.getDifficulty() != null ? q.getDifficulty() : "unknown",
                Collectors.counting()
            ));

        logger.info("测试集统计信息:");
        logger.info("- 文件: {}", Paths.get(filePath).getFileName());
        logger.info("- 总问题数: {}", questions.size());
        logger.info("- 难度分布: {}", difficultyCount);

        // 计算平均问题和答案长度
        double avgQuestionLength = questions.stream()
            .mapToInt(q -> q.getQuestion().length())
            .average()
            .orElse(0.0);

        double avgAnswerLength = questions.stream()
            .mapToInt(q -> q.getExpectedAnswer().length())
            .average()
            .orElse(0.0);

        logger.info("- 平均问题长度: {:.1f} 字符", avgQuestionLength);
        logger.info("- 平均答案长度: {:.1f} 字符", avgAnswerLength);
    }
}
