package com.kbtesting.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * 测试问题数据模型
 * 
 * <AUTHOR> Testing Team
 */
public class TestQuestion {
    
    @JsonProperty("question")
    private String question;
    
    @JsonProperty("answer")
    private String expectedAnswer;
    
    @JsonProperty("difficulty")
    private String difficulty;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    // 构造函数
    public TestQuestion() {}

    public TestQuestion(String question, String expectedAnswer, String difficulty) {
        this.question = question;
        this.expectedAnswer = expectedAnswer;
        this.difficulty = difficulty;
    }

    public TestQuestion(String question, String expectedAnswer, String difficulty, Map<String, Object> metadata) {
        this.question = question;
        this.expectedAnswer = expectedAnswer;
        this.difficulty = difficulty;
        this.metadata = metadata;
    }

    // Getters and Setters
    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getExpectedAnswer() {
        return expectedAnswer;
    }

    public void setExpectedAnswer(String expectedAnswer) {
        this.expectedAnswer = expectedAnswer;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    @Override
    public String toString() {
        return "TestQuestion{" +
                "question='" + question + '\'' +
                ", expectedAnswer='" + expectedAnswer + '\'' +
                ", difficulty='" + difficulty + '\'' +
                ", metadata=" + metadata +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TestQuestion that = (TestQuestion) o;

        if (!question.equals(that.question)) return false;
        return expectedAnswer.equals(that.expectedAnswer);
    }

    @Override
    public int hashCode() {
        int result = question.hashCode();
        result = 31 * result + expectedAnswer.hashCode();
        return result;
    }
}
