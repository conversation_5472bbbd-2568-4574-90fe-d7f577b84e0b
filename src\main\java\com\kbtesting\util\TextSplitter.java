package com.kbtesting.util;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 文本分割工具类
 * 将长文本分割成适合处理的块
 * 
 * <AUTHOR> Testing Team
 */
@Component
public class TextSplitter {
    
    // 分割符优先级（从高到低）
    private static final String[] SEPARATORS = {
        "\n\n",     // 段落分隔
        "\n",       // 行分隔
        "。",       // 中文句号
        "！",       // 中文感叹号
        "？",       // 中文问号
        ".",        // 英文句号
        "!",        // 英文感叹号
        "?",        // 英文问号
        ";",        // 分号
        ",",        // 逗号
        " ",        // 空格
        ""          // 字符级分割
    };

    /**
     * 分割文本
     */
    public List<String> splitText(String text, int chunkSize, int chunkOverlap) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        // 预处理文本
        text = preprocessText(text);
        
        // 如果文本长度小于块大小，直接返回
        if (text.length() <= chunkSize) {
            return List.of(text);
        }
        
        return splitTextRecursive(text, chunkSize, chunkOverlap, SEPARATORS, 0);
    }

    /**
     * 递归分割文本
     */
    private List<String> splitTextRecursive(String text, int chunkSize, int chunkOverlap, 
                                           String[] separators, int separatorIndex) {
        List<String> chunks = new ArrayList<>();
        
        if (text.length() <= chunkSize) {
            chunks.add(text);
            return chunks;
        }
        
        // 如果已经尝试了所有分割符，进行强制分割
        if (separatorIndex >= separators.length) {
            return forceSpitText(text, chunkSize, chunkOverlap);
        }
        
        String separator = separators[separatorIndex];
        String[] parts;
        
        if (separator.isEmpty()) {
            // 字符级分割
            parts = text.split("");
        } else {
            parts = text.split(Pattern.quote(separator), -1);
        }
        
        // 如果分割后只有一个部分，尝试下一个分割符
        if (parts.length == 1) {
            return splitTextRecursive(text, chunkSize, chunkOverlap, separators, separatorIndex + 1);
        }
        
        // 合并小的部分
        List<String> mergedParts = mergeParts(parts, separator, chunkSize);
        
        // 处理每个合并后的部分
        for (String part : mergedParts) {
            if (part.length() <= chunkSize) {
                chunks.add(part);
            } else {
                // 递归分割大的部分
                List<String> subChunks = splitTextRecursive(part, chunkSize, chunkOverlap, 
                                                          separators, separatorIndex + 1);
                chunks.addAll(subChunks);
            }
        }
        
        // 应用重叠
        return applyOverlap(chunks, chunkOverlap);
    }

    /**
     * 合并小的文本部分
     */
    private List<String> mergeParts(String[] parts, String separator, int chunkSize) {
        List<String> merged = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();
        
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            
            // 计算添加当前部分后的长度
            int newLength = currentChunk.length();
            if (newLength > 0 && !separator.isEmpty()) {
                newLength += separator.length();
            }
            newLength += part.length();
            
            if (newLength <= chunkSize) {
                // 可以添加到当前块
                if (currentChunk.length() > 0 && !separator.isEmpty()) {
                    currentChunk.append(separator);
                }
                currentChunk.append(part);
            } else {
                // 当前块已满，开始新块
                if (currentChunk.length() > 0) {
                    merged.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }
                currentChunk.append(part);
            }
        }
        
        // 添加最后一个块
        if (currentChunk.length() > 0) {
            merged.add(currentChunk.toString());
        }
        
        return merged;
    }

    /**
     * 强制分割文本（当所有分割符都无效时）
     */
    private List<String> forceSpitText(String text, int chunkSize, int chunkOverlap) {
        List<String> chunks = new ArrayList<>();
        
        for (int i = 0; i < text.length(); i += chunkSize - chunkOverlap) {
            int endIndex = Math.min(i + chunkSize, text.length());
            String chunk = text.substring(i, endIndex);
            chunks.add(chunk);
            
            if (endIndex >= text.length()) {
                break;
            }
        }
        
        return chunks;
    }

    /**
     * 应用重叠
     */
    private List<String> applyOverlap(List<String> chunks, int chunkOverlap) {
        if (chunkOverlap <= 0 || chunks.size() <= 1) {
            return chunks;
        }
        
        List<String> overlappedChunks = new ArrayList<>();
        overlappedChunks.add(chunks.get(0));
        
        for (int i = 1; i < chunks.size(); i++) {
            String prevChunk = chunks.get(i - 1);
            String currentChunk = chunks.get(i);
            
            // 从前一个块的末尾提取重叠部分
            String overlap = "";
            if (prevChunk.length() > chunkOverlap) {
                overlap = prevChunk.substring(prevChunk.length() - chunkOverlap);
            } else {
                overlap = prevChunk;
            }
            
            // 将重叠部分添加到当前块的开头
            String overlappedChunk = overlap + currentChunk;
            overlappedChunks.add(overlappedChunk);
        }
        
        return overlappedChunks;
    }

    /**
     * 预处理文本
     */
    private String preprocessText(String text) {
        // 统一换行符
        text = text.replaceAll("\\r\\n", "\n");
        text = text.replaceAll("\\r", "\n");
        
        // 移除多余的空白字符
        text = text.replaceAll("\\n{3,}", "\n\n");
        text = text.replaceAll("[ \\t]{2,}", " ");
        
        // 移除首尾空白
        text = text.trim();
        
        return text;
    }

    /**
     * 计算文本的token数量（简单估算）
     */
    public int estimateTokenCount(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        // 简单估算：中文字符按1个token计算，英文单词按1个token计算
        int chineseChars = 0;
        int englishWords = 0;
        
        for (char c : text.toCharArray()) {
            if (isChinese(c)) {
                chineseChars++;
            }
        }
        
        // 计算英文单词数
        String[] words = text.replaceAll("[\\u4e00-\\u9fa5]", "").split("\\s+");
        for (String word : words) {
            if (!word.trim().isEmpty()) {
                englishWords++;
            }
        }
        
        return chineseChars + englishWords;
    }

    /**
     * 判断字符是否为中文
     */
    private boolean isChinese(char c) {
        return c >= 0x4e00 && c <= 0x9fa5;
    }

    /**
     * 分割文本并返回详细信息
     */
    public List<TextChunk> splitTextWithInfo(String text, int chunkSize, int chunkOverlap) {
        List<String> chunks = splitText(text, chunkSize, chunkOverlap);
        List<TextChunk> textChunks = new ArrayList<>();
        
        for (int i = 0; i < chunks.size(); i++) {
            String chunk = chunks.get(i);
            TextChunk textChunk = new TextChunk(
                i,
                chunk,
                chunk.length(),
                estimateTokenCount(chunk)
            );
            textChunks.add(textChunk);
        }
        
        return textChunks;
    }

    /**
     * 文本块信息类
     */
    public static class TextChunk {
        private final int index;
        private final String content;
        private final int characterCount;
        private final int tokenCount;

        public TextChunk(int index, String content, int characterCount, int tokenCount) {
            this.index = index;
            this.content = content;
            this.characterCount = characterCount;
            this.tokenCount = tokenCount;
        }

        // Getters
        public int getIndex() { return index; }
        public String getContent() { return content; }
        public int getCharacterCount() { return characterCount; }
        public int getTokenCount() { return tokenCount; }

        @Override
        public String toString() {
            return String.format("TextChunk{index=%d, characterCount=%d, tokenCount=%d, content='%s...'}",
                               index, characterCount, tokenCount, 
                               content.length() > 50 ? content.substring(0, 50) : content);
        }
    }
}
