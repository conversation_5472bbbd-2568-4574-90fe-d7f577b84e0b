package examples;

import com.kbtesting.model.TestQuestion;
import com.kbtesting.service.TestsetGeneratorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import java.util.List;

/**
 * 测试集生成示例
 * 
 * 演示如何使用 TestsetGeneratorService.generateTestset() 方法
 * 
 * <AUTHOR> Testing Team
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.kbtesting")
public class TestsetGenerationExample implements CommandLineRunner {
    
    @Autowired
    private TestsetGeneratorService testsetGeneratorService;
    
    public static void main(String[] args) {
        SpringApplication.run(TestsetGenerationExample.class, args);
    }
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("🚀 测试集生成示例");
        System.out.println("==================");
        
        // 示例1：从文本文件生成测试集
        generateFromTextFile();
        
        // 示例2：从PDF文件生成测试集
        generateFromPdfFile();
        
        // 示例3：批量生成测试集
        batchGeneration();
        
        System.out.println("✅ 示例执行完成！");
    }
    
    /**
     * 从文本文件生成测试集
     */
    private void generateFromTextFile() {
        System.out.println("\n📝 示例1：从文本文件生成测试集");
        System.out.println("--------------------------------");
        
        String filePath = "data/test-documents/dify-knowledge-base-sample.txt";
        
        try {
            List<TestQuestion> questions = testsetGeneratorService.generateTestset(filePath);
            
            System.out.println("文件: " + filePath);
            System.out.println("生成的问题数量: " + questions.size());
            
            // 显示前3个问题
            for (int i = 0; i < Math.min(3, questions.size()); i++) {
                TestQuestion q = questions.get(i);
                System.out.println("\n问题 " + (i + 1) + ":");
                System.out.println("  Q: " + q.getQuestion());
                System.out.println("  A: " + q.getExpectedAnswer());
                System.out.println("  难度: " + q.getDifficulty());
            }
            
            if (questions.size() > 3) {
                System.out.println("... 还有 " + (questions.size() - 3) + " 个问题");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 从PDF文件生成测试集
     */
    private void generateFromPdfFile() {
        System.out.println("\n📄 示例2：从PDF文件生成测试集");
        System.out.println("--------------------------------");
        
        String filePath = "data/documents/sample.pdf";
        
        try {
            List<TestQuestion> questions = testsetGeneratorService.generateTestset(filePath);
            
            System.out.println("文件: " + filePath);
            System.out.println("生成的问题数量: " + questions.size());
            
            // 按难度分组统计
            long easyCount = questions.stream().filter(q -> "easy".equals(q.getDifficulty())).count();
            long mediumCount = questions.stream().filter(q -> "medium".equals(q.getDifficulty())).count();
            long hardCount = questions.stream().filter(q -> "hard".equals(q.getDifficulty())).count();
            
            System.out.println("难度分布:");
            System.out.println("  简单: " + easyCount + " 个");
            System.out.println("  中等: " + mediumCount + " 个");
            System.out.println("  困难: " + hardCount + " 个");
            
        } catch (Exception e) {
            System.err.println("❌ 生成失败: " + e.getMessage());
            System.out.println("💡 提示: 请确保PDF文件存在且可读取");
        }
    }
    
    /**
     * 批量生成测试集
     */
    private void batchGeneration() {
        System.out.println("\n📚 示例3：批量生成测试集");
        System.out.println("--------------------------------");
        
        String[] filePaths = {
            "data/test-documents/dify-knowledge-base-sample.txt",
            "data/documents/sample1.txt",
            "data/documents/sample2.txt"
        };
        
        int totalQuestions = 0;
        int successCount = 0;
        
        for (String filePath : filePaths) {
            try {
                System.out.println("\n处理文件: " + filePath);
                List<TestQuestion> questions = testsetGeneratorService.generateTestset(filePath);
                
                if (!questions.isEmpty()) {
                    System.out.println("✅ 成功生成 " + questions.size() + " 个问题");
                    totalQuestions += questions.size();
                    successCount++;
                } else {
                    System.out.println("⚠️ 未生成任何问题");
                }
                
            } catch (Exception e) {
                System.err.println("❌ 处理失败: " + e.getMessage());
            }
        }
        
        System.out.println("\n📊 批量处理结果:");
        System.out.println("成功处理: " + successCount + "/" + filePaths.length + " 个文件");
        System.out.println("总问题数: " + totalQuestions + " 个");
    }
    
    /**
     * 高级用法示例
     */
    @SuppressWarnings("unused")
    private void advancedUsage() {
        System.out.println("\n🔧 高级用法示例");
        System.out.println("--------------------------------");
        
        String filePath = "data/documents/technical-doc.pdf";
        
        try {
            // 生成测试集
            List<TestQuestion> questions = testsetGeneratorService.generateTestset(filePath);
            
            // 过滤特定难度的问题
            List<TestQuestion> hardQuestions = questions.stream()
                .filter(q -> "hard".equals(q.getDifficulty()))
                .toList();
            
            System.out.println("困难问题数量: " + hardQuestions.size());
            
            // 过滤特定长度的问题
            List<TestQuestion> longQuestions = questions.stream()
                .filter(q -> q.getQuestion().length() > 50)
                .toList();
            
            System.out.println("长问题数量: " + longQuestions.size());
            
            // 查找包含特定关键词的问题
            List<TestQuestion> aiQuestions = questions.stream()
                .filter(q -> q.getQuestion().toLowerCase().contains("人工智能") ||
                           q.getExpectedAnswer().toLowerCase().contains("人工智能"))
                .toList();
            
            System.out.println("AI相关问题数量: " + aiQuestions.size());
            
        } catch (Exception e) {
            System.err.println("❌ 高级用法示例失败: " + e.getMessage());
        }
    }
}
