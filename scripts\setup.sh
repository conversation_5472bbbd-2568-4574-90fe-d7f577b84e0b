#!/bin/bash

# 知识库自动化测试系统 - 安装脚本
# 用于快速设置项目环境

set -e

echo "🎯 知识库自动化测试系统 - 环境设置"
echo "=" | tr ' ' '=' | head -c 60 && echo

# 检查Java版本
echo "📋 检查Java环境..."
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    echo "   ✅ Java版本: $JAVA_VERSION"
    
    # 检查是否为Java 11+
    MAJOR_VERSION=$(echo $JAVA_VERSION | cut -d'.' -f1)
    if [ "$MAJOR_VERSION" -lt 11 ]; then
        echo "   ⚠️  警告: 建议使用Java 11或更高版本"
    fi
else
    echo "   ❌ 未找到Java，请先安装Java 11+"
    exit 1
fi

# 检查Maven
echo "📋 检查Maven环境..."
if command -v mvn &> /dev/null; then
    MVN_VERSION=$(mvn -version | head -n 1 | awk '{print $3}')
    echo "   ✅ Maven版本: $MVN_VERSION"
else
    echo "   ❌ 未找到Maven，请先安装Maven 3.6+"
    exit 1
fi

# 创建必要的目录结构
echo "📁 创建目录结构..."
directories=(
    "data/documents"
    "data/samples"
    "data/archive"
    "testsets/generated"
    "testsets/manual"
    "testsets/templates"
    "results/raw"
    "results/processed"
    "results/history"
    "reports/html"
    "reports/json"
    "reports/csv"
    "reports/summary"
    "logs/application"
    "logs/testing"
    "logs/error"
    "temp/uploads"
    "temp/processing"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "   📂 创建目录: $dir"
        
        # 创建.gitkeep文件以保持目录结构
        touch "$dir/.gitkeep"
    else
        echo "   ✅ 目录已存在: $dir"
    fi
done

# 检查配置文件
echo "⚙️  检查配置文件..."
CONFIG_FILE="src/main/resources/application.yml"
if [ -f "$CONFIG_FILE" ]; then
    echo "   ✅ 配置文件存在: $CONFIG_FILE"
    
    # 检查是否包含API密钥占位符
    if grep -q "your_dashscope_api_key" "$CONFIG_FILE"; then
        echo "   ⚠️  请在 $CONFIG_FILE 中设置您的DashScope API密钥"
    fi
    
    if grep -q "your_kb_api_key" "$CONFIG_FILE"; then
        echo "   ⚠️  请在 $CONFIG_FILE 中设置您的知识库API密钥"
    fi
else
    echo "   ❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 构建项目
echo "🔨 构建项目..."
if mvn clean compile > /dev/null 2>&1; then
    echo "   ✅ 项目编译成功"
else
    echo "   ❌ 项目编译失败，请检查依赖和配置"
    exit 1
fi

# 运行测试
echo "🧪 运行测试..."
if mvn test > /dev/null 2>&1; then
    echo "   ✅ 测试通过"
else
    echo "   ⚠️  测试失败，请检查代码"
fi

# 打包项目
echo "📦 打包项目..."
if mvn package -DskipTests > /dev/null 2>&1; then
    echo "   ✅ 项目打包成功"
    JAR_FILE=$(find target -name "*.jar" -not -name "*-sources.jar" | head -n 1)
    if [ -f "$JAR_FILE" ]; then
        echo "   📄 生成的JAR文件: $JAR_FILE"
    fi
else
    echo "   ❌ 项目打包失败"
    exit 1
fi

# 创建启动脚本
echo "📝 创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash

# 知识库自动化测试系统启动脚本

JAR_FILE=$(find target -name "*.jar" -not -name "*-sources.jar" | head -n 1)

if [ ! -f "$JAR_FILE" ]; then
    echo "❌ 未找到JAR文件，请先运行 mvn package"
    exit 1
fi

echo "🚀 启动知识库自动化测试系统..."
echo "📄 JAR文件: $JAR_FILE"
echo "🌐 Web界面: http://localhost:8080/kb-testing"
echo ""

# 设置JVM参数
JAVA_OPTS="-Xmx2g -Xms512m -XX:+UseG1GC"

# 启动应用
java $JAVA_OPTS -jar "$JAR_FILE" "$@"
EOF

chmod +x start.sh
echo "   ✅ 启动脚本已创建: start.sh"

# 创建示例数据
echo "📋 创建示例文件..."
cat > data/documents/README.md << 'EOF'
# 示例文档目录

请将您要处理的文档文件放在此目录中。

支持的文件格式：
- PDF (.pdf)
- Word文档 (.docx)
- HTML文件 (.html, .htm)
- 文本文件 (.txt)

## 使用方法

1. 将文档文件复制到此目录
2. 运行测试集生成命令
3. 查看生成的测试集文件

## 注意事项

- 确保文档文件编码为UTF-8
- 文档内容应该包含可以提问的知识点
- 避免上传过大的文件（建议小于50MB）
EOF

# 完成设置
echo ""
echo "🎉 环境设置完成!"
echo "=" | tr ' ' '=' | head -c 60 && echo
echo ""
echo "📋 下一步操作:"
echo "   1. 编辑配置文件设置API密钥:"
echo "      vi src/main/resources/application.yml"
echo ""
echo "   2. 将文档文件放入 data/documents/ 目录"
echo ""
echo "   3. 启动应用:"
echo "      ./start.sh"
echo ""
echo "   4. 或使用命令行模式:"
echo "      ./start.sh help"
echo ""
echo "🌐 Web界面地址: http://localhost:8080/kb-testing"
echo "📚 API文档: http://localhost:8080/kb-testing/swagger-ui.html"
echo ""
echo "💡 如有问题，请查看 README.md 文档"
echo ""
