# 测试环境配置

server:
  port: 0  # 随机端口

# API配置（测试环境使用模拟值）
api:
  dashscope:
    api-key: "test-api-key"
    model: "qwen-max"
    base-url: "http://localhost:8080/mock"
  
  knowledge-base:
    base-url: "http://localhost:8080/mock"
    api-key: "test-kb-api-key"

# 测试集生成配置
testset-generation:
  chunk-size: 500
  chunk-overlap: 100
  questions-per-chunk: 2

# 知识库测试配置
knowledge-base-testing:
  max-retries: 1
  batch-size: 5
  parallel-workers: 1

# 文件路径配置（测试环境）
paths:
  input-documents: "src/test/resources/test-documents"
  testsets: "target/test-testsets"
  test-results: "target/test-results"
  reports: "target/test-reports"
  logs: "target/test-logs"
  temp: "target/test-temp"

# 日志配置
logging:
  level:
    com.kbtesting: DEBUG
    org.springframework: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
