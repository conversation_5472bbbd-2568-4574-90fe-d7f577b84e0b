package com.kbtesting.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbtesting.config.AppConfig;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * DashScope API服务
 * 
 * <AUTHOR> Testing Team
 */
@Service
public class DashScopeService {
    
    private static final Logger logger = LoggerFactory.getLogger(DashScopeService.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private OkHttpClient httpClient;

    public DashScopeService() {
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();
    }

    /**
     * 生成文本
     */
    public String generateText(String prompt) throws IOException {
        AppConfig.Api.Dashscope dashscopeConfig = appConfig.getApi().getDashscope();
        
        // 构建请求体
        String requestBody = objectMapper.writeValueAsString(Map.of(
            "model", dashscopeConfig.getModel(),
            "input", Map.of("prompt", prompt),
            "parameters", Map.of(
                "max_tokens", 2000,
                "temperature", 0.7,
                "top_p", 0.9
            )
        ));
        
        Request request = new Request.Builder()
            .url(dashscopeConfig.getBaseUrl() + "/services/aigc/text-generation/generation")
            .addHeader("Authorization", "Bearer " + dashscopeConfig.getApiKey())
            .addHeader("Content-Type", "application/json")
            .addHeader("X-DashScope-Async", "enable")
            .post(RequestBody.create(requestBody, MediaType.get("application/json")))
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("DashScope API调用失败: " + response.code() + " " + response.message());
            }
            
            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            // 检查响应状态
            String status = jsonNode.path("output").path("finish_reason").asText();
            if (!"stop".equals(status)) {
                logger.warn("DashScope响应状态异常: {}", status);
            }
            
            // 提取生成的文本
            return jsonNode.path("output").path("text").asText();
            
        } catch (Exception e) {
            logger.error("调用DashScope API失败: {}", e.getMessage());
            throw new IOException("DashScope API调用失败", e);
        }
    }

    /**
     * 评估答案质量
     */
    public String evaluateAnswer(String question, String expectedAnswer, String actualAnswer) throws IOException {
        String prompt = appConfig.getAnswerEvaluation().getEvaluationPromptTemplate()
            .replace("{question}", question)
            .replace("{expectedAnswer}", expectedAnswer)
            .replace("{actualAnswer}", actualAnswer);
        
        return generateText(prompt);
    }

    /**
     * 计算文本嵌入向量
     */
    public double[] getEmbedding(String text) throws IOException {
        AppConfig.Api.Dashscope dashscopeConfig = appConfig.getApi().getDashscope();
        
        String requestBody = objectMapper.writeValueAsString(Map.of(
            "model", dashscopeConfig.getEmbeddingModel(),
            "input", Map.of("texts", new String[]{text})
        ));
        
        Request request = new Request.Builder()
            .url(dashscopeConfig.getBaseUrl() + "/services/embeddings/text-embedding/text-embedding")
            .addHeader("Authorization", "Bearer " + dashscopeConfig.getApiKey())
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(requestBody, MediaType.get("application/json")))
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("DashScope嵌入API调用失败: " + response.code());
            }
            
            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            // 提取嵌入向量
            JsonNode embeddingNode = jsonNode.path("output").path("embeddings").get(0).path("embedding");
            double[] embedding = new double[embeddingNode.size()];
            
            for (int i = 0; i < embeddingNode.size(); i++) {
                embedding[i] = embeddingNode.get(i).asDouble();
            }
            
            return embedding;
            
        } catch (Exception e) {
            logger.error("调用DashScope嵌入API失败: {}", e.getMessage());
            throw new IOException("DashScope嵌入API调用失败", e);
        }
    }

    /**
     * 计算两个向量的余弦相似度
     */
    public double calculateCosineSimilarity(double[] vectorA, double[] vectorB) {
        if (vectorA.length != vectorB.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }
        
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        
        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += Math.pow(vectorA[i], 2);
            normB += Math.pow(vectorB[i], 2);
        }
        
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    /**
     * 计算文本相似度
     */
    public double calculateTextSimilarity(String text1, String text2) {
        try {
            double[] embedding1 = getEmbedding(text1);
            double[] embedding2 = getEmbedding(text2);
            return calculateCosineSimilarity(embedding1, embedding2);
            
        } catch (IOException e) {
            logger.error("计算文本相似度失败: {}", e.getMessage());
            // 降级到简单的字符串相似度计算
            return calculateSimpleTextSimilarity(text1, text2);
        }
    }

    /**
     * 简单的文本相似度计算（备用方案）
     */
    private double calculateSimpleTextSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) {
            return 0.0;
        }

        if (text1.trim().isEmpty() || text2.trim().isEmpty()) {
            return 0.0;
        }

        String[] words1 = text1.toLowerCase(Locale.ROOT).split("\\s+");
        String[] words2 = text2.toLowerCase(Locale.ROOT).split("\\s+");

        Set<String> set1 = new HashSet<>(Arrays.asList(words1));
        Set<String> set2 = new HashSet<>(Arrays.asList(words2));

        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
}
