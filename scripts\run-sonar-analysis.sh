#!/bin/bash

# Sonar 代码质量分析脚本
# 使用方法: ./scripts/run-sonar-analysis.sh [sonar-server-url] [sonar-token]

set -e

# 默认配置
DEFAULT_SONAR_HOST_URL="http://localhost:9000"
DEFAULT_SONAR_TOKEN=""

# 获取参数
SONAR_HOST_URL=${1:-$DEFAULT_SONAR_HOST_URL}
SONAR_TOKEN=${2:-$DEFAULT_SONAR_TOKEN}

echo "🔍 开始 Sonar 代码质量分析"
echo "================================"
echo "Sonar 服务器: $SONAR_HOST_URL"
echo "项目: knowledge-base-testing"
echo ""

# 检查 Maven 是否可用
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到 Maven 命令"
    exit 1
fi

# 检查 Java 版本
echo "📋 检查环境..."
java -version
mvn -version
echo ""

# 1. 清理之前的构建
echo "🧹 清理之前的构建..."
mvn clean
echo ""

# 2. 编译项目
echo "🔨 编译项目..."
mvn compile test-compile
echo ""

# 3. 运行测试并生成覆盖率报告
echo "🧪 运行测试并生成覆盖率报告..."
mvn test jacoco:report
echo ""

# 4. 运行代码质量检查
echo "📊 运行代码质量检查..."

# Checkstyle 检查
echo "  - 运行 Checkstyle 检查..."
mvn checkstyle:check || echo "⚠️ Checkstyle 检查发现问题，但继续执行"

# PMD 检查
echo "  - 运行 PMD 检查..."
mvn pmd:check || echo "⚠️ PMD 检查发现问题，但继续执行"

# SpotBugs 检查
echo "  - 运行 SpotBugs 检查..."
mvn spotbugs:check || echo "⚠️ SpotBugs 检查发现问题，但继续执行"

echo ""

# 5. 运行 Sonar 分析
echo "🚀 运行 Sonar 分析..."

if [ -n "$SONAR_TOKEN" ]; then
    mvn sonar:sonar \
        -Dsonar.host.url="$SONAR_HOST_URL" \
        -Dsonar.login="$SONAR_TOKEN"
else
    echo "⚠️ 未提供 Sonar Token，使用默认认证"
    mvn sonar:sonar \
        -Dsonar.host.url="$SONAR_HOST_URL"
fi

echo ""

# 6. 生成报告摘要
echo "📋 生成报告摘要..."
echo "================================"

# 检查测试结果
if [ -f "target/surefire-reports/TEST-*.xml" ]; then
    TEST_COUNT=$(find target/surefire-reports -name "TEST-*.xml" -exec grep -l "testcase" {} \; | wc -l)
    echo "✅ 测试报告: 找到 $TEST_COUNT 个测试文件"
else
    echo "⚠️ 测试报告: 未找到测试结果文件"
fi

# 检查覆盖率报告
if [ -f "target/site/jacoco/jacoco.xml" ]; then
    echo "✅ 覆盖率报告: target/site/jacoco/jacoco.xml"
    if command -v xmllint &> /dev/null; then
        COVERAGE=$(xmllint --xpath "string(//report/counter[@type='INSTRUCTION']/@covered)" target/site/jacoco/jacoco.xml 2>/dev/null || echo "N/A")
        TOTAL=$(xmllint --xpath "string(//report/counter[@type='INSTRUCTION']/@missed)" target/site/jacoco/jacoco.xml 2>/dev/null || echo "N/A")
        if [ "$COVERAGE" != "N/A" ] && [ "$TOTAL" != "N/A" ]; then
            PERCENTAGE=$(echo "scale=2; $COVERAGE / ($COVERAGE + $TOTAL) * 100" | bc 2>/dev/null || echo "N/A")
            echo "  代码覆盖率: ${PERCENTAGE}%"
        fi
    fi
else
    echo "⚠️ 覆盖率报告: 未找到覆盖率报告"
fi

# 检查 Checkstyle 报告
if [ -f "target/checkstyle-result.xml" ]; then
    echo "✅ Checkstyle 报告: target/checkstyle-result.xml"
    if command -v xmllint &> /dev/null; then
        ERROR_COUNT=$(xmllint --xpath "count(//error)" target/checkstyle-result.xml 2>/dev/null || echo "N/A")
        echo "  Checkstyle 问题数: $ERROR_COUNT"
    fi
else
    echo "⚠️ Checkstyle 报告: 未找到报告文件"
fi

# 检查 PMD 报告
if [ -f "target/pmd.xml" ]; then
    echo "✅ PMD 报告: target/pmd.xml"
    if command -v xmllint &> /dev/null; then
        VIOLATION_COUNT=$(xmllint --xpath "count(//violation)" target/pmd.xml 2>/dev/null || echo "N/A")
        echo "  PMD 问题数: $VIOLATION_COUNT"
    fi
else
    echo "⚠️ PMD 报告: 未找到报告文件"
fi

# 检查 SpotBugs 报告
if [ -f "target/spotbugsXml.xml" ]; then
    echo "✅ SpotBugs 报告: target/spotbugsXml.xml"
    if command -v xmllint &> /dev/null; then
        BUG_COUNT=$(xmllint --xpath "count(//BugInstance)" target/spotbugsXml.xml 2>/dev/null || echo "N/A")
        echo "  SpotBugs 问题数: $BUG_COUNT"
    fi
else
    echo "⚠️ SpotBugs 报告: 未找到报告文件"
fi

echo ""
echo "🎉 Sonar 分析完成！"
echo ""
echo "📊 查看结果:"
echo "- Sonar 仪表板: $SONAR_HOST_URL/dashboard?id=knowledge-base-testing"
echo "- 本地覆盖率报告: target/site/jacoco/index.html"
echo "- Checkstyle 报告: target/site/checkstyle.html"
echo "- PMD 报告: target/site/pmd.html"
echo "- SpotBugs 报告: target/site/spotbugs.html"
echo ""
echo "💡 提示:"
echo "1. 如果是首次运行，请确保 Sonar 服务器已启动"
echo "2. 检查 sonar-project.properties 配置是否正确"
echo "3. 根据报告修复代码质量问题"
