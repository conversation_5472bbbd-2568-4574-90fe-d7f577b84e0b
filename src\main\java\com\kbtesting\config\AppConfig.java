package com.kbtesting.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 应用配置类
 * 
 * <AUTHOR> Testing Team
 */
@Configuration
@ConfigurationProperties(prefix = "")
public class AppConfig {

    private Api api = new Api();
    private TestsetGeneration testsetGeneration = new TestsetGeneration();
    private KnowledgeBaseTesting knowledgeBaseTesting = new KnowledgeBaseTesting();
    private DifyTesting difyTesting = new DifyTesting();
    private AnswerEvaluation answerEvaluation = new AnswerEvaluation();
    private Paths paths = new Paths();
    private Reporting reporting = new Reporting();
    private Performance performance = new Performance();
    private QualityControl qualityControl = new QualityControl();

    // Getters and Setters
    public Api getApi() { return api; }
    public void setApi(Api api) { this.api = api; }

    public TestsetGeneration getTestsetGeneration() { return testsetGeneration; }
    public void setTestsetGeneration(TestsetGeneration testsetGeneration) { this.testsetGeneration = testsetGeneration; }

    public KnowledgeBaseTesting getKnowledgeBaseTesting() { return knowledgeBaseTesting; }
    public void setKnowledgeBaseTesting(KnowledgeBaseTesting knowledgeBaseTesting) { this.knowledgeBaseTesting = knowledgeBaseTesting; }

    public DifyTesting getDifyTesting() { return difyTesting; }
    public void setDifyTesting(DifyTesting difyTesting) { this.difyTesting = difyTesting; }

    public AnswerEvaluation getAnswerEvaluation() { return answerEvaluation; }
    public void setAnswerEvaluation(AnswerEvaluation answerEvaluation) { this.answerEvaluation = answerEvaluation; }

    public Paths getPaths() { return paths; }
    public void setPaths(Paths paths) { this.paths = paths; }

    public Reporting getReporting() { return reporting; }
    public void setReporting(Reporting reporting) { this.reporting = reporting; }

    public Performance getPerformance() { return performance; }
    public void setPerformance(Performance performance) { this.performance = performance; }

    public QualityControl getQualityControl() { return qualityControl; }
    public void setQualityControl(QualityControl qualityControl) { this.qualityControl = qualityControl; }

    // 内部配置类
    public static class Api {
        private Dashscope dashscope = new Dashscope();
        private KnowledgeBase knowledgeBase = new KnowledgeBase();
        private Dify dify = new Dify();

        public Dashscope getDashscope() { return dashscope; }
        public void setDashscope(Dashscope dashscope) { this.dashscope = dashscope; }

        public KnowledgeBase getKnowledgeBase() { return knowledgeBase; }
        public void setKnowledgeBase(KnowledgeBase knowledgeBase) { this.knowledgeBase = knowledgeBase; }

        public Dify getDify() { return dify; }
        public void setDify(Dify dify) { this.dify = dify; }

        public static class Dashscope {
            private String apiKey;
            private String model = "qwen-max";
            private String embeddingModel = "text-embedding-v1";
            private String baseUrl = "https://dashscope.aliyuncs.com/api/v1";
            private int timeout = 30000;

            // Getters and Setters
            public String getApiKey() { return apiKey; }
            public void setApiKey(String apiKey) { this.apiKey = apiKey; }

            public String getModel() { return model; }
            public void setModel(String model) { this.model = model; }

            public String getEmbeddingModel() { return embeddingModel; }
            public void setEmbeddingModel(String embeddingModel) { this.embeddingModel = embeddingModel; }

            public String getBaseUrl() { return baseUrl; }
            public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

            public int getTimeout() { return timeout; }
            public void setTimeout(int timeout) { this.timeout = timeout; }
        }

        public static class KnowledgeBase {
            private String baseUrl = "http://localhost:3000/api";
            private String apiKey;
            private int timeout = 30000;

            // Getters and Setters
            public String getBaseUrl() { return baseUrl; }
            public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

            public String getApiKey() { return apiKey; }
            public void setApiKey(String apiKey) { this.apiKey = apiKey; }

            public int getTimeout() { return timeout; }
            public void setTimeout(int timeout) { this.timeout = timeout; }
        }

        public static class Dify {
            private String baseUrl = "http://localhost/v1";
            private String apiKey;
            private int timeout = 30000;

            // Getters and Setters
            public String getBaseUrl() { return baseUrl; }
            public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

            public String getApiKey() { return apiKey; }
            public void setApiKey(String apiKey) { this.apiKey = apiKey; }

            public int getTimeout() { return timeout; }
            public void setTimeout(int timeout) { this.timeout = timeout; }
        }
    }

    public static class TestsetGeneration {
        private int chunkSize = 1000;
        private int chunkOverlap = 200;
        private int questionsPerChunk = 3;
        private List<String> supportedFormats = List.of(".pdf", ".docx", ".html", ".htm", ".txt");
        private String questionPromptTemplate;

        // Getters and Setters
        public int getChunkSize() { return chunkSize; }
        public void setChunkSize(int chunkSize) { this.chunkSize = chunkSize; }

        public int getChunkOverlap() { return chunkOverlap; }
        public void setChunkOverlap(int chunkOverlap) { this.chunkOverlap = chunkOverlap; }

        public int getQuestionsPerChunk() { return questionsPerChunk; }
        public void setQuestionsPerChunk(int questionsPerChunk) { this.questionsPerChunk = questionsPerChunk; }

        public List<String> getSupportedFormats() { return supportedFormats; }
        public void setSupportedFormats(List<String> supportedFormats) { this.supportedFormats = supportedFormats; }

        public String getQuestionPromptTemplate() { return questionPromptTemplate; }
        public void setQuestionPromptTemplate(String questionPromptTemplate) { this.questionPromptTemplate = questionPromptTemplate; }
    }

    public static class KnowledgeBaseTesting {
        private int maxRetries = 3;
        private int retryDelay = 1000;
        private int batchSize = 10;
        private int parallelWorkers = 3;
        private Map<String, Object> queryParams = Map.of(
            "topK", 5,
            "scoreThreshold", 0.7,
            "maxTokens", 1000
        );

        // Getters and Setters
        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = maxRetries; }

        public int getRetryDelay() { return retryDelay; }
        public void setRetryDelay(int retryDelay) { this.retryDelay = retryDelay; }

        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }

        public int getParallelWorkers() { return parallelWorkers; }
        public void setParallelWorkers(int parallelWorkers) { this.parallelWorkers = parallelWorkers; }

        public Map<String, Object> getQueryParams() { return queryParams; }
        public void setQueryParams(Map<String, Object> queryParams) { this.queryParams = queryParams; }
    }

    public static class DifyTesting {
        private KnowledgeBaseConfig knowledgeBase = new KnowledgeBaseConfig();
        private DocumentProcessing documentProcessing = new DocumentProcessing();
        private Retrieval retrieval = new Retrieval();
        private TestSettings testSettings = new TestSettings();

        // Getters and Setters
        public KnowledgeBaseConfig getKnowledgeBase() { return knowledgeBase; }
        public void setKnowledgeBase(KnowledgeBaseConfig knowledgeBase) { this.knowledgeBase = knowledgeBase; }

        public DocumentProcessing getDocumentProcessing() { return documentProcessing; }
        public void setDocumentProcessing(DocumentProcessing documentProcessing) { this.documentProcessing = documentProcessing; }

        public Retrieval getRetrieval() { return retrieval; }
        public void setRetrieval(Retrieval retrieval) { this.retrieval = retrieval; }

        public TestSettings getTestSettings() { return testSettings; }
        public void setTestSettings(TestSettings testSettings) { this.testSettings = testSettings; }

        public static class KnowledgeBaseConfig {
            private String defaultIndexingTechnique = "high_quality";
            private String defaultEmbeddingModel = "text-embedding-ada-002";
            private String defaultEmbeddingProvider = "openai";

            public String getDefaultIndexingTechnique() { return defaultIndexingTechnique; }
            public void setDefaultIndexingTechnique(String defaultIndexingTechnique) { this.defaultIndexingTechnique = defaultIndexingTechnique; }

            public String getDefaultEmbeddingModel() { return defaultEmbeddingModel; }
            public void setDefaultEmbeddingModel(String defaultEmbeddingModel) { this.defaultEmbeddingModel = defaultEmbeddingModel; }

            public String getDefaultEmbeddingProvider() { return defaultEmbeddingProvider; }
            public void setDefaultEmbeddingProvider(String defaultEmbeddingProvider) { this.defaultEmbeddingProvider = defaultEmbeddingProvider; }
        }

        public static class DocumentProcessing {
            private int chunkSize = 1000;
            private int chunkOverlap = 200;
            private String separator = "\n\n";

            public int getChunkSize() { return chunkSize; }
            public void setChunkSize(int chunkSize) { this.chunkSize = chunkSize; }

            public int getChunkOverlap() { return chunkOverlap; }
            public void setChunkOverlap(int chunkOverlap) { this.chunkOverlap = chunkOverlap; }

            public String getSeparator() { return separator; }
            public void setSeparator(String separator) { this.separator = separator; }
        }

        public static class Retrieval {
            private int defaultTopK = 5;
            private double defaultScoreThreshold = 0.7;
            private String searchMethod = "semantic_search";

            public int getDefaultTopK() { return defaultTopK; }
            public void setDefaultTopK(int defaultTopK) { this.defaultTopK = defaultTopK; }

            public double getDefaultScoreThreshold() { return defaultScoreThreshold; }
            public void setDefaultScoreThreshold(double defaultScoreThreshold) { this.defaultScoreThreshold = defaultScoreThreshold; }

            public String getSearchMethod() { return searchMethod; }
            public void setSearchMethod(String searchMethod) { this.searchMethod = searchMethod; }
        }

        public static class TestSettings {
            private boolean cleanupAfterTest = true;
            private boolean waitForIndexing = true;
            private int indexingTimeout = 300;
            private String testDocumentPath = "data/test-documents";

            public boolean isCleanupAfterTest() { return cleanupAfterTest; }
            public void setCleanupAfterTest(boolean cleanupAfterTest) { this.cleanupAfterTest = cleanupAfterTest; }

            public boolean isWaitForIndexing() { return waitForIndexing; }
            public void setWaitForIndexing(boolean waitForIndexing) { this.waitForIndexing = waitForIndexing; }

            public int getIndexingTimeout() { return indexingTimeout; }
            public void setIndexingTimeout(int indexingTimeout) { this.indexingTimeout = indexingTimeout; }

            public String getTestDocumentPath() { return testDocumentPath; }
            public void setTestDocumentPath(String testDocumentPath) { this.testDocumentPath = testDocumentPath; }
        }
    }

    public static class AnswerEvaluation {
        private double similarityThreshold = 0.8;
        private List<String> evaluationMetrics = List.of("similarity", "relevance", "completeness", "accuracy");
        private String evaluationPromptTemplate;

        // Getters and Setters
        public double getSimilarityThreshold() { return similarityThreshold; }
        public void setSimilarityThreshold(double similarityThreshold) { this.similarityThreshold = similarityThreshold; }

        public List<String> getEvaluationMetrics() { return evaluationMetrics; }
        public void setEvaluationMetrics(List<String> evaluationMetrics) { this.evaluationMetrics = evaluationMetrics; }

        public String getEvaluationPromptTemplate() { return evaluationPromptTemplate; }
        public void setEvaluationPromptTemplate(String evaluationPromptTemplate) { this.evaluationPromptTemplate = evaluationPromptTemplate; }
    }

    public static class Paths {
        private String inputDocuments = "data/documents";
        private String testsets = "testsets/generated";
        private String testResults = "results/raw";
        private String reports = "reports";
        private String logs = "logs/application";
        private String temp = "temp";

        // Getters and Setters
        public String getInputDocuments() { return inputDocuments; }
        public void setInputDocuments(String inputDocuments) { this.inputDocuments = inputDocuments; }

        public String getTestsets() { return testsets; }
        public void setTestsets(String testsets) { this.testsets = testsets; }

        public String getTestResults() { return testResults; }
        public void setTestResults(String testResults) { this.testResults = testResults; }

        public String getReports() { return reports; }
        public void setReports(String reports) { this.reports = reports; }

        public String getLogs() { return logs; }
        public void setLogs(String logs) { this.logs = logs; }

        public String getTemp() { return temp; }
        public void setTemp(String temp) { this.temp = temp; }
    }

    public static class Reporting {
        private List<String> formats = List.of("html", "json", "csv");
        private boolean includeCharts = true;
        private boolean includeDetailedResults = true;

        // Getters and Setters
        public List<String> getFormats() { return formats; }
        public void setFormats(List<String> formats) { this.formats = formats; }

        public boolean isIncludeCharts() { return includeCharts; }
        public void setIncludeCharts(boolean includeCharts) { this.includeCharts = includeCharts; }

        public boolean isIncludeDetailedResults() { return includeDetailedResults; }
        public void setIncludeDetailedResults(boolean includeDetailedResults) { this.includeDetailedResults = includeDetailedResults; }
    }

    public static class Performance {
        private String maxMemoryUsage = "2GB";
        private boolean cacheEnabled = true;
        private int cacheSize = 1000;

        // Getters and Setters
        public String getMaxMemoryUsage() { return maxMemoryUsage; }
        public void setMaxMemoryUsage(String maxMemoryUsage) { this.maxMemoryUsage = maxMemoryUsage; }

        public boolean isCacheEnabled() { return cacheEnabled; }
        public void setCacheEnabled(boolean cacheEnabled) { this.cacheEnabled = cacheEnabled; }

        public int getCacheSize() { return cacheSize; }
        public void setCacheSize(int cacheSize) { this.cacheSize = cacheSize; }
    }

    public static class QualityControl {
        private int minQuestionLength = 10;
        private int maxQuestionLength = 200;
        private int minAnswerLength = 5;
        private int maxAnswerLength = 500;
        private double duplicateThreshold = 0.9;

        // Getters and Setters
        public int getMinQuestionLength() { return minQuestionLength; }
        public void setMinQuestionLength(int minQuestionLength) { this.minQuestionLength = minQuestionLength; }

        public int getMaxQuestionLength() { return maxQuestionLength; }
        public void setMaxQuestionLength(int maxQuestionLength) { this.maxQuestionLength = maxQuestionLength; }

        public int getMinAnswerLength() { return minAnswerLength; }
        public void setMinAnswerLength(int minAnswerLength) { this.minAnswerLength = minAnswerLength; }

        public int getMaxAnswerLength() { return maxAnswerLength; }
        public void setMaxAnswerLength(int maxAnswerLength) { this.maxAnswerLength = maxAnswerLength; }

        public double getDuplicateThreshold() { return duplicateThreshold; }
        public void setDuplicateThreshold(double duplicateThreshold) { this.duplicateThreshold = duplicateThreshold; }
    }
}
