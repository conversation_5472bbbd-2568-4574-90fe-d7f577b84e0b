@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 知识库自动化测试系统 - Windows安装脚本

echo 🎯 知识库自动化测试系统 - 环境设置
echo ============================================================

REM 检查Java版本
echo 📋 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo    ❌ 未找到Java，请先安装Java 11+
    pause
    exit /b 1
) else (
    for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
        set JAVA_VERSION=%%g
        set JAVA_VERSION=!JAVA_VERSION:"=!
    )
    echo    ✅ Java版本: !JAVA_VERSION!
)

REM 检查Maven
echo 📋 检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo    ❌ 未找到Maven，请先安装Maven 3.6+
    pause
    exit /b 1
) else (
    for /f "tokens=3" %%g in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
        set MVN_VERSION=%%g
    )
    echo    ✅ Maven版本: !MVN_VERSION!
)

REM 创建必要的目录结构
echo 📁 创建目录结构...
set directories=data\documents data\samples data\archive testsets\generated testsets\manual testsets\templates results\raw results\processed results\history reports\html reports\json reports\csv reports\summary logs\application logs\testing logs\error temp\uploads temp\processing

for %%d in (%directories%) do (
    if not exist "%%d" (
        mkdir "%%d" >nul 2>&1
        echo    📂 创建目录: %%d
        echo. > "%%d\.gitkeep"
    ) else (
        echo    ✅ 目录已存在: %%d
    )
)

REM 检查配置文件
echo ⚙️  检查配置文件...
set CONFIG_FILE=src\main\resources\application.yml
if exist "%CONFIG_FILE%" (
    echo    ✅ 配置文件存在: %CONFIG_FILE%
    
    findstr /c:"your_dashscope_api_key" "%CONFIG_FILE%" >nul
    if !errorlevel! equ 0 (
        echo    ⚠️  请在 %CONFIG_FILE% 中设置您的DashScope API密钥
    )
    
    findstr /c:"your_kb_api_key" "%CONFIG_FILE%" >nul
    if !errorlevel! equ 0 (
        echo    ⚠️  请在 %CONFIG_FILE% 中设置您的知识库API密钥
    )
) else (
    echo    ❌ 配置文件不存在: %CONFIG_FILE%
    pause
    exit /b 1
)

REM 构建项目
echo 🔨 构建项目...
mvn clean compile >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 项目编译成功
) else (
    echo    ❌ 项目编译失败，请检查依赖和配置
    pause
    exit /b 1
)

REM 运行测试
echo 🧪 运行测试...
mvn test >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 测试通过
) else (
    echo    ⚠️  测试失败，请检查代码
)

REM 打包项目
echo 📦 打包项目...
mvn package -DskipTests >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 项目打包成功
    for /f %%i in ('dir /b target\*.jar ^| findstr /v sources') do (
        echo    📄 生成的JAR文件: target\%%i
        set JAR_FILE=target\%%i
    )
) else (
    echo    ❌ 项目打包失败
    pause
    exit /b 1
)

REM 创建启动脚本
echo 📝 创建启动脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo.
echo REM 知识库自动化测试系统启动脚本
echo.
echo for /f %%%%i in ^('dir /b target\*.jar ^| findstr /v sources'^) do ^(
echo     set JAR_FILE=target\%%%%i
echo     goto :found
echo ^)
echo.
echo echo ❌ 未找到JAR文件，请先运行 mvn package
echo pause
echo exit /b 1
echo.
echo :found
echo echo 🚀 启动知识库自动化测试系统...
echo echo 📄 JAR文件: %%JAR_FILE%%
echo echo 🌐 Web界面: http://localhost:8080/kb-testing
echo echo.
echo.
echo REM 设置JVM参数
echo set JAVA_OPTS=-Xmx2g -Xms512m -XX:+UseG1GC
echo.
echo REM 启动应用
echo java %%JAVA_OPTS%% -jar "%%JAR_FILE%%" %%*
) > start.bat

echo    ✅ 启动脚本已创建: start.bat

REM 创建示例数据
echo 📋 创建示例文件...
(
echo # 示例文档目录
echo.
echo 请将您要处理的文档文件放在此目录中。
echo.
echo 支持的文件格式：
echo - PDF ^(.pdf^)
echo - Word文档 ^(.docx^)
echo - HTML文件 ^(.html, .htm^)
echo - 文本文件 ^(.txt^)
echo.
echo ## 使用方法
echo.
echo 1. 将文档文件复制到此目录
echo 2. 运行测试集生成命令
echo 3. 查看生成的测试集文件
echo.
echo ## 注意事项
echo.
echo - 确保文档文件编码为UTF-8
echo - 文档内容应该包含可以提问的知识点
echo - 避免上传过大的文件（建议小于50MB）
) > data\documents\README.md

REM 完成设置
echo.
echo 🎉 环境设置完成!
echo ============================================================
echo.
echo 📋 下一步操作:
echo    1. 编辑配置文件设置API密钥:
echo       notepad src\main\resources\application.yml
echo.
echo    2. 将文档文件放入 data\documents\ 目录
echo.
echo    3. 启动应用:
echo       start.bat
echo.
echo    4. 或使用命令行模式:
echo       start.bat help
echo.
echo 🌐 Web界面地址: http://localhost:8080/kb-testing
echo 📚 API文档: http://localhost:8080/kb-testing/swagger-ui.html
echo.
echo 💡 如有问题，请查看 README.md 文档
echo.
pause
